#!/usr/bin/env python3
"""
Setup script for Safe RL Meta-Learning Framework
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text() if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
if requirements_path.exists():
    with open(requirements_path) as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
else:
    requirements = [
        "torch>=2.0.0",
        "numpy>=1.21.0",
        "gymnasium>=0.29.0",
        "matplotlib>=3.5.0",
        "rich>=12.0.0",
        "pyyaml>=6.0",
        "pandas>=1.3.0",
        "scipy>=1.7.0",
        "tqdm>=4.64.0",
    ]

setup(
    name="safe-rl-meta-learning",
    version="1.0.0",
    description="Safe Reinforcement Learning with Meta-Learning Framework",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Safe RL Research Team",
    author_email="<EMAIL>",
    url="https://github.com/saferl/safe-rl-meta-learning",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "isort>=5.10.0",
            "mypy>=0.991",
            "flake8>=5.0.0",
        ],
        "safety_gym": [
            "safety-gym",
            "mujoco>=2.3.0",
        ],
        "visualization": [
            "plotly>=5.0.0",
            "seaborn>=0.11.0",
            "jupyter>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "safe-rl-train=main:main",
            "safe-rl-experiments=experiments.main_experiments:main",
            "safe-rl-analyze=analysis.generate_report:main",
            "safe-rl-verify=scripts.verify_installation:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.yaml", "*.json", "*.md"],
    },
)
