"""
Configuration Management System
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class AgentConfig:
    """Agent configuration"""
    learning_rate: float = 3e-4
    batch_size: int = 64
    n_epochs: int = 10
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_range: float = 0.2
    ent_coef: float = 0.01
    vf_coef: float = 0.5
    max_grad_norm: float = 0.5
    
    # Meta-learning specific
    meta_learning_rate: float = 1e-4
    meta_batch_size: int = 32
    adaptation_steps: int = 5
    
    # Safety specific
    safety_coef: float = 1.0
    constraint_threshold: float = 0.1
    lagrange_multiplier: float = 0.1

@dataclass
class EnvironmentConfig:
    """Environment configuration"""
    max_episode_steps: int = 1000
    reward_scale: float = 1.0
    safety_budget: float = 0.1
    constraint_type: str = "hard"  # "hard" or "soft"
    
    # Environment specific parameters
    env_params: Dict[str, Any] = None

@dataclass
class TrainingConfig:
    """Training configuration"""
    total_timesteps: int = 100000
    eval_freq: int = 5000
    n_eval_episodes: int = 10
    save_freq: int = 10000
    log_freq: int = 1000
    
    # Checkpointing
    save_model: bool = True
    save_replay_buffer: bool = False
    
    # Early stopping
    early_stopping: bool = False
    patience: int = 10
    min_improvement: float = 0.01

@dataclass
class ExperimentConfig:
    """Full experiment configuration"""
    agent: AgentConfig
    environment: EnvironmentConfig
    training: TrainingConfig
    
    # Experiment metadata
    experiment_name: str = "default"
    seed: int = 42
    device: str = "auto"
    
    # Logging
    use_wandb: bool = False
    use_tensorboard: bool = True
    log_level: str = "INFO"

class ConfigManager:
    """Manages configuration loading and saving"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Create default configs if they don't exist
        self._create_default_configs()
    
    def _create_default_configs(self):
        """Create default configuration files"""
        
        # Default agent configs
        agent_configs = {
            "ppo": AgentConfig(),
            "meta_ppo": AgentConfig(
                meta_learning_rate=1e-4,
                meta_batch_size=32,
                adaptation_steps=5
            ),
            "safe_ppo": AgentConfig(
                safety_coef=2.0,
                constraint_threshold=0.05
            ),
            "transformer_ppo": AgentConfig(
                learning_rate=1e-4,
                batch_size=32,
                meta_learning_rate=5e-5
            )
        }
        
        # Default environment configs
        env_configs = {
            "cartpole_safe": EnvironmentConfig(
                max_episode_steps=500,
                safety_budget=0.1,
                env_params={"pole_angle_limit": 0.2}
            ),
            "lunar_safe": EnvironmentConfig(
                max_episode_steps=1000,
                safety_budget=0.15,
                env_params={"crash_penalty": -100}
            ),
            "healthcare_sim": EnvironmentConfig(
                max_episode_steps=100,
                safety_budget=0.05,
                env_params={"dose_limit": 10.0}
            ),
            "safety_gym": EnvironmentConfig(
                max_episode_steps=1000,
                safety_budget=0.1,
                env_params={"task": "goal", "robot": "point"}
            )
        }
        
        # Save configs
        for name, config in agent_configs.items():
            self._save_config(config, self.config_dir / "agents" / f"{name}.yaml")
        
        for name, config in env_configs.items():
            self._save_config(config, self.config_dir / "environments" / f"{name}.yaml")
    
    def _save_config(self, config, path: Path):
        """Save configuration to file"""
        path.parent.mkdir(parents=True, exist_ok=True)
        
        if hasattr(config, '__dict__'):
            config_dict = asdict(config) if hasattr(config, '__dataclass_fields__') else config.__dict__
        else:
            config_dict = config
            
        with open(path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from file"""
        path = Path(config_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(path, 'r') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"Loaded config from {config_path}")
        return config
    
    def get_default_config(self, env_name: str, agent_name: str) -> Dict[str, Any]:
        """Get default configuration for environment and agent"""
        
        # Load agent config
        agent_config_path = self.config_dir / "agents" / f"{agent_name}.yaml"
        if agent_config_path.exists():
            agent_config = self.load_config(agent_config_path)
        else:
            logger.warning(f"Agent config not found: {agent_name}, using default")
            agent_config = asdict(AgentConfig())
        
        # Load environment config
        env_config_path = self.config_dir / "environments" / f"{env_name}.yaml"
        if env_config_path.exists():
            env_config = self.load_config(env_config_path)
        else:
            logger.warning(f"Environment config not found: {env_name}, using default")
            env_config = asdict(EnvironmentConfig())
        
        # Combine configs
        config = {
            "agent": agent_config,
            "environment": env_config,
            "training": asdict(TrainingConfig()),
        }
        
        return config
    
    def save_experiment_config(self, config: Dict[str, Any], experiment_name: str):
        """Save experiment configuration"""
        config_path = self.config_dir / "experiments" / f"{experiment_name}.yaml"
        self._save_config(config, config_path)
        logger.info(f"Saved experiment config to {config_path}")
    
    def list_configs(self, config_type: str = None) -> Dict[str, list]:
        """List available configurations"""
        configs = {
            "agents": [],
            "environments": [],
            "experiments": []
        }
        
        for config_dir in ["agents", "environments", "experiments"]:
            config_path = self.config_dir / config_dir
            if config_path.exists():
                configs[config_dir] = [
                    f.stem for f in config_path.glob("*.yaml")
                ]
        
        if config_type:
            return configs.get(config_type, [])
        
        return configs
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate configuration"""
        required_keys = ["agent", "environment", "training"]
        
        for key in required_keys:
            if key not in config:
                logger.error(f"Missing required config section: {key}")
                return False
        
        # Validate agent config
        agent_config = config["agent"]
        if "learning_rate" not in agent_config or agent_config["learning_rate"] <= 0:
            logger.error("Invalid learning rate in agent config")
            return False
        
        # Validate environment config
        env_config = config["environment"]
        if "max_episode_steps" not in env_config or env_config["max_episode_steps"] <= 0:
            logger.error("Invalid max_episode_steps in environment config")
            return False
        
        # Validate training config
        training_config = config["training"]
        if "total_timesteps" not in training_config or training_config["total_timesteps"] <= 0:
            logger.error("Invalid total_timesteps in training config")
            return False
        
        logger.info("Configuration validation passed")
        return True
    
    def merge_configs(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge two configurations, with override taking precedence"""
        
        def deep_merge(base: dict, override: dict) -> dict:
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        return deep_merge(base_config, override_config)
