#!/usr/bin/env python3
"""
Main entry point for Safe RL Meta-Learning Framework
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any

import torch
import numpy as np
from rich.console import Console
from rich.logging import RichHandler

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.device_manager import DeviceManager
from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from agents.agent_factory import AgentFactory
from environments.env_factory import EnvironmentFactory
from experiments.training_runner import TrainingRunner

console = Console()

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Safe RL Meta-Learning Framework")
    
    # Environment settings
    parser.add_argument("--env", type=str, default="cartpole_safe",
                       choices=["cartpole_safe", "lunar_safe", "healthcare_sim", "safety_gym"],
                       help="Environment to use")
    
    # Agent settings
    parser.add_argument("--agent", type=str, default="meta_ppo",
                       choices=["ppo", "meta_ppo", "safe_ppo", "transformer_ppo"],
                       help="Agent type to use")
    
    # Training settings
    parser.add_argument("--total_timesteps", type=int, default=100000,
                       help="Total training timesteps")
    parser.add_argument("--eval_freq", type=int, default=5000,
                       help="Evaluation frequency")
    parser.add_argument("--n_eval_episodes", type=int, default=10,
                       help="Number of evaluation episodes")
    
    # Hardware settings
    parser.add_argument("--gpu", action="store_true",
                       help="Use GPU if available")
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cpu, cuda)")
    
    # Experiment settings
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--experiment_name", type=str, default=None,
                       help="Experiment name for logging")
    parser.add_argument("--save_model", action="store_true",
                       help="Save trained model")
    
    # Configuration
    parser.add_argument("--config", type=str, default=None,
                       help="Path to config file")
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    # Safety settings
    parser.add_argument("--safety_budget", type=float, default=0.1,
                       help="Safety constraint budget")
    parser.add_argument("--meta_learning", action="store_true",
                       help="Enable meta-learning for safety")
    
    return parser.parse_args()

def setup_experiment(args) -> Dict[str, Any]:
    """Setup experiment configuration"""
    
    # Setup device
    device_manager = DeviceManager()
    if args.device == "auto":
        device = device_manager.get_best_device(prefer_gpu=args.gpu)
    else:
        device = torch.device(args.device)
    
    console.print(f"[green]Using device: {device}[/green]")
    
    # Setup random seeds
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    if device.type == "cuda":
        torch.cuda.manual_seed(args.seed)
        torch.cuda.manual_seed_all(args.seed)
    
    # Load configuration
    config_manager = ConfigManager()
    if args.config:
        config = config_manager.load_config(args.config)
    else:
        config = config_manager.get_default_config(args.env, args.agent)
    
    # Override config with command line arguments
    config.update({
        "env_name": args.env,
        "agent_type": args.agent,
        "total_timesteps": args.total_timesteps,
        "eval_freq": args.eval_freq,
        "n_eval_episodes": args.n_eval_episodes,
        "device": device,
        "seed": args.seed,
        "safety_budget": args.safety_budget,
        "meta_learning": args.meta_learning,
        "save_model": args.save_model,
    })
    
    # Setup experiment name
    if args.experiment_name is None:
        config["experiment_name"] = f"{args.agent}_{args.env}_{args.seed}"
    else:
        config["experiment_name"] = args.experiment_name
    
    return config

def main():
    """Main training loop"""
    args = parse_args()
    
    # Setup logging
    logger = setup_logger(
        name="safe_rl_main",
        level=getattr(logging, args.log_level),
        console=console
    )
    
    try:
        # Setup experiment
        config = setup_experiment(args)
        logger.info(f"Starting experiment: {config['experiment_name']}")
        
        # Create environment
        env_factory = EnvironmentFactory()
        env = env_factory.create_environment(config["env_name"], config)
        logger.info(f"Created environment: {config['env_name']}")
        
        # Create agent
        agent_factory = AgentFactory()
        agent = agent_factory.create_agent(
            config["agent_type"], 
            env, 
            config
        )
        logger.info(f"Created agent: {config['agent_type']}")
        
        # Setup training runner
        runner = TrainingRunner(agent, env, config, logger)
        
        # Start training
        console.print("[bold green]Starting training...[/bold green]")
        results = runner.run()
        
        # Print results summary
        console.print("\n[bold blue]Training Complete![/bold blue]")
        console.print(f"Final reward: {results['final_reward']:.2f}")
        console.print(f"Safety violations: {results['total_violations']}")
        console.print(f"Training time: {results['training_time']:.2f}s")
        
        if config["save_model"]:
            model_path = runner.save_model()
            console.print(f"Model saved to: {model_path}")
        
        logger.info("Experiment completed successfully")
        
    except Exception as e:
        logger.error(f"Experiment failed: {str(e)}")
        console.print(f"[red]Error: {str(e)}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()
