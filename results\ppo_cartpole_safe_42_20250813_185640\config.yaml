agent:
  adaptation_steps: 5
  batch_size: 64
  clip_range: 0.2
  constraint_threshold: 0.1
  ent_coef: 0.01
  gae_lambda: 0.95
  gamma: 0.99
  lagrange_multiplier: 0.1
  learning_rate: 0.0003
  max_grad_norm: 0.5
  meta_batch_size: 32
  meta_learning_rate: 0.0001
  n_epochs: 10
  safety_coef: 1.0
  vf_coef: 0.5
agent_type: ppo
device: !!python/object/apply:torch.device
- cuda
- 0
env_name: cartpole_safe
environment:
  constraint_type: hard
  env_params:
    pole_angle_limit: 0.2
  max_episode_steps: 500
  reward_scale: 1.0
  safety_budget: 0.1
eval_freq: 5000
experiment_name: ppo_cartpole_safe_42
meta_learning: false
n_eval_episodes: 10
safety_budget: 0.1
save_model: false
seed: 42
total_timesteps: 5000
training:
  early_stopping: false
  eval_freq: 5000
  log_freq: 1000
  min_improvement: 0.01
  n_eval_episodes: 10
  patience: 10
  save_freq: 10000
  save_model: true
  save_replay_buffer: false
  total_timesteps: 100000
