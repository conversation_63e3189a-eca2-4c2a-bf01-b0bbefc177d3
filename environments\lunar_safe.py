"""
Safe Lunar Lander Environment with Safety Constraints
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class SafeLunarLanderEnv(gym.Env):
    """
    Safe Lunar Lander environment with fuel and crash constraints
    
    Safety constraints:
    - Fuel consumption limits
    - Safe landing velocity
    - Altitude safety margins
    - Landing zone constraints
    """
    
    metadata = {"render_modes": ["human", "rgb_array"], "render_fps": 50}
    
    def __init__(self, 
                 max_episode_steps: int = 1000,
                 safety_budget: float = 0.15,
                 crash_penalty: float = -100,
                 fuel_limit: float = 100.0,
                 safe_landing_velocity: float = 1.0,
                 safe_altitude_margin: float = 0.5,
                 constraint_type: str = "hard",
                 render_mode: Optional[str] = None,
                 **kwargs):
        
        super().__init__()
        
        # Environment parameters
        self.max_episode_steps = max_episode_steps
        self.safety_budget = safety_budget
        self.crash_penalty = crash_penalty
        self.fuel_limit = fuel_limit
        self.safe_landing_velocity = safe_landing_velocity
        self.safe_altitude_margin = safe_altitude_margin
        self.constraint_type = constraint_type
        self.render_mode = render_mode
        
        # Create base LunarLander environment
        try:
            self.base_env = gym.make("LunarLander-v2", render_mode=render_mode)
        except:
            # Fallback if LunarLander not available
            logger.warning("LunarLander-v2 not available, using simplified physics")
            self.base_env = None
        
        # Action and observation spaces
        self.action_space = spaces.Discrete(4)  # 0: nothing, 1: left, 2: main, 3: right
        
        # Extended observations: original + fuel + safety indicators
        if self.base_env:
            base_obs_dim = self.base_env.observation_space.shape[0]
        else:
            base_obs_dim = 8  # Standard LunarLander observation size
        
        # Observations: [base_obs..., fuel_remaining, altitude_safety, velocity_safety]
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(base_obs_dim + 3,), dtype=np.float32
        )
        
        # State variables
        self.state = None
        self.fuel_consumed = 0.0
        self.current_step = 0
        
        # Safety tracking
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        self.crashed = False
        
        # Physics parameters (if using simplified physics)
        self.gravity = -10.0
        self.main_engine_power = 13.0
        self.side_engine_power = 0.6
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset environment to initial state"""
        super().reset(seed=seed)
        
        if self.base_env:
            base_obs, base_info = self.base_env.reset(seed=seed, options=options)
            self.state = base_obs
        else:
            # Simplified reset
            self.state = np.array([
                0.0,  # x position
                1.5,  # y position (altitude)
                0.0,  # x velocity
                0.0,  # y velocity
                0.0,  # angle
                0.0,  # angular velocity
                1.0,  # left leg contact
                1.0   # right leg contact
            ], dtype=np.float32)
        
        # Reset safety tracking
        self.fuel_consumed = 0.0
        self.current_step = 0
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        self.crashed = False
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Execute one step in the environment"""
        assert self.action_space.contains(action), f"Invalid action {action}"
        
        # Calculate fuel consumption
        fuel_cost = self._calculate_fuel_cost(action)
        self.fuel_consumed += fuel_cost
        
        # Execute action in base environment or simplified physics
        if self.base_env:
            base_obs, base_reward, terminated, truncated, base_info = self.base_env.step(action)
            self.state = base_obs
        else:
            base_obs, base_reward, terminated, truncated, base_info = self._simplified_step(action)
            self.state = base_obs
        
        self.current_step += 1
        
        # Check safety constraints
        safety_violation, constraint_violation, cost = self._check_safety_constraints()
        
        # Check if crashed
        if self.base_env:
            self.crashed = base_info.get("crashed", False)
        else:
            self.crashed = self.state[1] <= 0 and (abs(self.state[2]) > self.safe_landing_velocity or 
                                                  abs(self.state[3]) > self.safe_landing_velocity)
        
        # Modify reward with safety considerations
        reward = self._compute_safe_reward(base_reward, safety_violation, constraint_violation)
        
        # Update safety tracking
        if safety_violation:
            self.safety_violations += 1
        if constraint_violation:
            self.constraint_violations += 1
        self.cumulative_cost += cost
        
        # Check truncation (max steps or fuel limit)
        truncated = truncated or self.current_step >= self.max_episode_steps or self.fuel_consumed >= self.fuel_limit
        
        # Get observation and info
        observation = self._get_observation()
        info = self._get_info()
        info.update({
            "safety_violation": safety_violation,
            "constraint_violation": constraint_violation,
            "cost": cost,
            "cumulative_cost": self.cumulative_cost,
            "fuel_consumed": self.fuel_consumed,
            "crashed": self.crashed,
        })
        
        return observation, reward, terminated, truncated, info
    
    def _calculate_fuel_cost(self, action: int) -> float:
        """Calculate fuel cost for action"""
        if action == 0:  # No action
            return 0.0
        elif action == 1 or action == 3:  # Side engines
            return 0.1
        elif action == 2:  # Main engine
            return 0.3
        return 0.0
    
    def _simplified_step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Simplified physics step if LunarLander not available"""
        x, y, vx, vy, angle, angular_vel, left_contact, right_contact = self.state
        
        # Apply gravity
        vy += self.gravity * 0.02
        
        # Apply engine forces
        if action == 1:  # Left engine
            vx += self.side_engine_power * 0.02
        elif action == 2:  # Main engine
            vy += self.main_engine_power * 0.02
        elif action == 3:  # Right engine
            vx -= self.side_engine_power * 0.02
        
        # Update position
        x += vx * 0.02
        y += vy * 0.02
        
        # Simple collision detection
        if y <= 0:
            y = 0
            vy = 0
            left_contact = 1.0
            right_contact = 1.0
        
        # Update state
        new_state = np.array([x, y, vx, vy, angle, angular_vel, left_contact, right_contact], dtype=np.float32)
        
        # Simple reward
        reward = -0.1  # Small penalty for each step
        if y <= 0:  # Landed
            if abs(vx) < 1.0 and abs(vy) < 1.0:
                reward += 100  # Landing bonus
            else:
                reward -= 100  # Crash penalty
        
        terminated = y <= 0
        truncated = False
        info = {}
        
        return new_state, reward, terminated, truncated, info
    
    def _check_safety_constraints(self) -> Tuple[bool, bool, float]:
        """Check safety constraints and return violations and cost"""
        x, y, vx, vy = self.state[:4]
        
        safety_violation = False
        constraint_violation = False
        cost = 0.0
        
        # Fuel constraint
        if self.fuel_consumed > self.fuel_limit * 0.9:
            constraint_violation = True
            cost += (self.fuel_consumed - self.fuel_limit * 0.9) * 0.1
        
        # Altitude safety margin
        if y < self.safe_altitude_margin and y > 0:
            safety_violation = True
            cost += (self.safe_altitude_margin - y) * 2.0
        
        # Velocity constraints
        velocity_magnitude = np.sqrt(vx**2 + vy**2)
        if velocity_magnitude > self.safe_landing_velocity * 2:
            safety_violation = True
            cost += (velocity_magnitude - self.safe_landing_velocity * 2) * 1.0
        
        # Landing zone constraint (simple)
        if abs(x) > 1.0:
            constraint_violation = True
            cost += (abs(x) - 1.0) * 0.5
        
        # Crash detection
        if self.crashed:
            safety_violation = True
            cost += 10.0
        
        return safety_violation, constraint_violation, cost
    
    def _compute_safe_reward(self, base_reward: float, safety_violation: bool, constraint_violation: bool) -> float:
        """Compute reward with safety penalties"""
        reward = base_reward
        
        # Safety penalties
        if safety_violation:
            reward += self.crash_penalty * 0.1  # Scaled crash penalty
        
        if constraint_violation:
            reward -= 5.0  # Constraint violation penalty
        
        # Fuel efficiency bonus
        fuel_efficiency = 1.0 - (self.fuel_consumed / self.fuel_limit)
        reward += fuel_efficiency * 2.0
        
        # Safe operation bonus
        if not safety_violation and not constraint_violation:
            reward += 1.0
        
        return reward
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation with safety indicators"""
        base_obs = self.state
        
        # Fuel remaining (normalized)
        fuel_remaining = max(0.0, (self.fuel_limit - self.fuel_consumed) / self.fuel_limit)
        
        # Altitude safety indicator
        altitude_safety = 1.0 if self.state[1] > self.safe_altitude_margin else 0.0
        
        # Velocity safety indicator
        velocity_magnitude = np.sqrt(self.state[2]**2 + self.state[3]**2)
        velocity_safety = 1.0 if velocity_magnitude <= self.safe_landing_velocity * 2 else 0.0
        
        # Combine observations
        observation = np.concatenate([
            base_obs,
            [fuel_remaining, altitude_safety, velocity_safety]
        ]).astype(np.float32)
        
        return observation
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info"""
        return {
            "step": self.current_step,
            "safety_violations": self.safety_violations,
            "constraint_violations": self.constraint_violations,
            "cumulative_cost": self.cumulative_cost,
            "fuel_consumed": self.fuel_consumed,
            "fuel_remaining": self.fuel_limit - self.fuel_consumed,
            "altitude": self.state[1],
            "velocity_x": self.state[2],
            "velocity_y": self.state[3],
            "velocity_magnitude": np.sqrt(self.state[2]**2 + self.state[3]**2),
            "crashed": self.crashed,
            "safety_budget_used": self.cumulative_cost / (self.safety_budget * self.max_episode_steps),
        }
    
    def render(self):
        """Render the environment"""
        if self.base_env and hasattr(self.base_env, 'render'):
            return self.base_env.render()
        else:
            # Simple text-based rendering
            if self.render_mode == "human":
                print(f"Step: {self.current_step}, Altitude: {self.state[1]:.2f}, "
                      f"Fuel: {self.fuel_consumed:.1f}/{self.fuel_limit}, "
                      f"Violations: {self.safety_violations}")
    
    def close(self):
        """Close the environment"""
        if self.base_env:
            self.base_env.close()
