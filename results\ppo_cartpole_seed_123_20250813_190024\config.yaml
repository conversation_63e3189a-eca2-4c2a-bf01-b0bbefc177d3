agent:
  adaptation_steps: 5
  batch_size: 64
  clip_range: 0.2
  constraint_threshold: 0.1
  ent_coef: 0.01
  gae_lambda: 0.95
  gamma: 0.99
  lagrange_multiplier: 0.1
  learning_rate: 0.0003
  max_grad_norm: 0.5
  meta_batch_size: 32
  meta_learning_rate: 0.0001
  n_epochs: 10
  safety_coef: 1.0
  vf_coef: 0.5
device: !!python/object/apply:torch.device
- cuda
- 0
early_stopping: false
environment:
  constraint_type: hard
  env_params:
    pole_angle_limit: 0.2
  max_episode_steps: 500
  reward_scale: 1.0
  safety_budget: 0.1
eval_freq: 5000
experiment_name: ppo_cartpole_seed_123
n_eval_episodes: 10
save_model: true
seed: 123
total_timesteps: 50000
training:
  early_stopping: false
  eval_freq: 5000
  log_freq: 1000
  min_improvement: 0.01
  n_eval_episodes: 10
  patience: 10
  save_freq: 10000
  save_model: true
  save_replay_buffer: false
  total_timesteps: 100000
