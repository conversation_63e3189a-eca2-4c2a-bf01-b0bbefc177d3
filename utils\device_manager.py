"""
Device Management for GPU/CPU with Windows optimization
"""

import logging
import platform
import subprocess
import warnings
from typing import Optional, Dict, Any

import torch
import psutil
from rich.console import Console

console = Console()
logger = logging.getLogger(__name__)

class DeviceManager:
    """Manages device selection and optimization for Windows/GPU/CPU"""
    
    def __init__(self):
        self.system_info = self._get_system_info()
        self.gpu_info = self._get_gpu_info()
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.architecture()[0],
            "processor": platform.processor(),
            "cpu_count": psutil.cpu_count(),
            "memory_gb": psutil.virtual_memory().total / (1024**3),
        }
    
    def _get_gpu_info(self) -> Dict[str, Any]:
        """Get GPU information"""
        gpu_info = {
            "cuda_available": torch.cuda.is_available(),
            "cuda_version": None,
            "gpu_count": 0,
            "gpu_names": [],
            "gpu_memory": [],
        }
        
        if torch.cuda.is_available():
            gpu_info.update({
                "cuda_version": torch.version.cuda,
                "gpu_count": torch.cuda.device_count(),
                "gpu_names": [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())],
                "gpu_memory": [torch.cuda.get_device_properties(i).total_memory / (1024**3) 
                              for i in range(torch.cuda.device_count())],
            })
            
            # Try to get more detailed GPU info on Windows
            if platform.system() == "Windows":
                try:
                    import nvidia_ml_py3 as nvml
                    nvml.nvmlInit()
                    gpu_info["nvidia_driver"] = nvml.nvmlSystemGetDriverVersion().decode()
                except ImportError:
                    logger.debug("nvidia-ml-py3 not available for detailed GPU info")
                except Exception as e:
                    logger.debug(f"Could not get NVIDIA driver info: {e}")
        
        return gpu_info
    
    def get_best_device(self, prefer_gpu: bool = True) -> torch.device:
        """Get the best available device"""
        
        if prefer_gpu and self.gpu_info["cuda_available"]:
            # Select best GPU (highest memory)
            if self.gpu_info["gpu_count"] > 1:
                best_gpu = max(range(self.gpu_info["gpu_count"]), 
                              key=lambda i: self.gpu_info["gpu_memory"][i])
                device = torch.device(f"cuda:{best_gpu}")
                console.print(f"[green]Selected GPU {best_gpu}: {self.gpu_info['gpu_names'][best_gpu]} "
                            f"({self.gpu_info['gpu_memory'][best_gpu]:.1f}GB)[/green]")
            else:
                device = torch.device("cuda:0")
                console.print(f"[green]Selected GPU: {self.gpu_info['gpu_names'][0]} "
                            f"({self.gpu_info['gpu_memory'][0]:.1f}GB)[/green]")
        else:
            device = torch.device("cpu")
            console.print(f"[yellow]Using CPU: {self.system_info['processor']} "
                        f"({self.system_info['cpu_count']} cores)[/yellow]")
        
        return device
    
    def optimize_for_device(self, device: torch.device) -> Dict[str, Any]:
        """Get optimization settings for the device"""
        
        optimizations = {
            "num_workers": 0,  # Default for Windows
            "pin_memory": False,
            "torch_threads": None,
            "mixed_precision": False,
        }
        
        if device.type == "cuda":
            # GPU optimizations
            optimizations.update({
                "pin_memory": True,
                "mixed_precision": True,
                "torch_threads": min(4, self.system_info["cpu_count"]),
            })
            
            # Enable optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            
        else:
            # CPU optimizations
            cpu_cores = self.system_info["cpu_count"]
            optimizations.update({
                "torch_threads": cpu_cores,
                "num_workers": min(4, cpu_cores // 2) if cpu_cores > 2 else 0,
            })
            
            # Set PyTorch threads
            torch.set_num_threads(optimizations["torch_threads"])
            
        # Windows-specific optimizations
        if platform.system() == "Windows":
            # Disable multiprocessing on Windows for stability
            optimizations["num_workers"] = 0
            
        return optimizations
    
    def check_memory_usage(self, device: torch.device) -> Dict[str, float]:
        """Check current memory usage"""
        
        memory_info = {}
        
        if device.type == "cuda":
            # GPU memory
            memory_info.update({
                "gpu_allocated_gb": torch.cuda.memory_allocated(device) / (1024**3),
                "gpu_reserved_gb": torch.cuda.memory_reserved(device) / (1024**3),
                "gpu_total_gb": torch.cuda.get_device_properties(device).total_memory / (1024**3),
            })
            memory_info["gpu_utilization"] = memory_info["gpu_allocated_gb"] / memory_info["gpu_total_gb"]
        
        # System memory
        memory = psutil.virtual_memory()
        memory_info.update({
            "system_used_gb": memory.used / (1024**3),
            "system_total_gb": memory.total / (1024**3),
            "system_utilization": memory.percent / 100.0,
        })
        
        return memory_info
    
    def clear_gpu_cache(self):
        """Clear GPU cache if available"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.debug("GPU cache cleared")
    
    def print_system_info(self):
        """Print detailed system information"""
        console.print("\n[bold blue]System Information[/bold blue]")
        console.print(f"Platform: {self.system_info['platform']} {self.system_info['platform_version']}")
        console.print(f"Architecture: {self.system_info['architecture']}")
        console.print(f"CPU: {self.system_info['processor']}")
        console.print(f"CPU Cores: {self.system_info['cpu_count']}")
        console.print(f"Memory: {self.system_info['memory_gb']:.1f} GB")
        
        console.print("\n[bold blue]GPU Information[/bold blue]")
        if self.gpu_info["cuda_available"]:
            console.print(f"CUDA Available: ✓")
            console.print(f"CUDA Version: {self.gpu_info['cuda_version']}")
            console.print(f"GPU Count: {self.gpu_info['gpu_count']}")
            for i, (name, memory) in enumerate(zip(self.gpu_info['gpu_names'], self.gpu_info['gpu_memory'])):
                console.print(f"GPU {i}: {name} ({memory:.1f} GB)")
        else:
            console.print("CUDA Available: ✗")
        
        console.print("\n[bold blue]PyTorch Information[/bold blue]")
        console.print(f"PyTorch Version: {torch.__version__}")
        console.print(f"CUDA Version: {torch.version.cuda}")
        console.print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def get_device_manager() -> DeviceManager:
    """Get global device manager instance"""
    if not hasattr(get_device_manager, "_instance"):
        get_device_manager._instance = DeviceManager()
    return get_device_manager._instance
