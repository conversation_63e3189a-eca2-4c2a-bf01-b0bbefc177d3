"""
Base Agent Classes for Safe RL
"""

from abc import ABC, abstractmethod
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional, List
import numpy as np
import torch
import torch.nn as nn
from gymnasium import spaces
import logging

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """Abstract base class for all RL agents"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 config: Dict[str, Any], device: torch.device):
        self.observation_space = observation_space
        self.action_space = action_space
        self.config = config
        self.device = device
        
        # Training state
        self.training_step = 0
        self.episode_count = 0
        
        # Metrics tracking
        self.metrics = {
            "total_reward": 0.0,
            "episode_length": 0,
            "safety_violations": 0,
            "constraint_violations": 0,
        }
        
        # Initialize networks
        self._build_networks()
        
    @abstractmethod
    def _build_networks(self):
        """Build neural networks"""
        pass
    
    @abstractmethod
    def select_action(self, observation: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """Select action given observation"""
        pass
    
    @abstractmethod
    def update(self, batch: Dict[str, Any]) -> Dict[str, float]:
        """Update agent with batch of experiences"""
        pass
    
    @abstractmethod
    def save(self, path: str):
        """Save agent state"""
        pass
    
    @abstractmethod
    def load(self, path: str):
        """Load agent state"""
        pass
    
    def reset_episode(self):
        """Reset episode-specific metrics"""
        self.metrics = {
            "total_reward": 0.0,
            "episode_length": 0,
            "safety_violations": 0,
            "constraint_violations": 0,
        }
    
    def step(self, reward: float, done: bool, info: Dict[str, Any]):
        """Update metrics after environment step"""
        self.metrics["total_reward"] += reward
        self.metrics["episode_length"] += 1
        
        # Track safety violations
        if "safety_violation" in info and info["safety_violation"]:
            self.metrics["safety_violations"] += 1
        
        if "constraint_violation" in info and info["constraint_violation"]:
            self.metrics["constraint_violations"] += 1
        
        if done:
            self.episode_count += 1
    
    def get_metrics(self) -> Dict[str, float]:
        """Get current metrics"""
        return self.metrics.copy()

class SafeAgent(BaseAgent):
    """Base class for safe RL agents with constraint handling"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 config: Dict[str, Any], device: torch.device):
        super().__init__(observation_space, action_space, config, device)
        
        # Safety-specific parameters
        self.safety_budget = config.get("safety_budget", 0.1)
        self.constraint_threshold = config.get("constraint_threshold", 0.1)
        self.safety_coef = config.get("safety_coef", 1.0)
        
        # Lagrange multiplier for constraint optimization
        self.lagrange_multiplier = torch.tensor(
            config.get("lagrange_multiplier", 0.1), 
            device=device, 
            requires_grad=True
        )
        
        # Safety metrics
        self.safety_metrics = {
            "cumulative_violations": 0,
            "violation_rate": 0.0,
            "constraint_value": 0.0,
            "lagrange_multiplier": self.lagrange_multiplier.item(),
        }
    
    @abstractmethod
    def compute_safety_loss(self, batch: Dict[str, Any]) -> torch.Tensor:
        """Compute safety-related loss"""
        pass
    
    def update_lagrange_multiplier(self, constraint_violation: float):
        """Update Lagrange multiplier based on constraint violation"""
        with torch.no_grad():
            # Gradient ascent on Lagrange multiplier
            lr = self.config.get("lagrange_lr", 0.01)
            self.lagrange_multiplier += lr * (constraint_violation - self.constraint_threshold)
            self.lagrange_multiplier.clamp_(min=0.0)  # Keep non-negative
        
        self.safety_metrics["lagrange_multiplier"] = self.lagrange_multiplier.item()
    
    def get_safety_metrics(self) -> Dict[str, float]:
        """Get safety-specific metrics"""
        return self.safety_metrics.copy()

class MetaLearningAgent(SafeAgent):
    """Base class for meta-learning agents"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 config: Dict[str, Any], device: torch.device):
        super().__init__(observation_space, action_space, config, device)
        
        # Meta-learning parameters
        self.meta_learning_rate = config.get("meta_learning_rate", 1e-4)
        self.meta_batch_size = config.get("meta_batch_size", 32)
        self.adaptation_steps = config.get("adaptation_steps", 5)
        
        # Task context
        self.task_context = None
        self.task_history = []
        
        # Meta-learning metrics
        self.meta_metrics = {
            "adaptation_loss": 0.0,
            "meta_loss": 0.0,
            "task_similarity": 0.0,
        }
    
    @abstractmethod
    def adapt_to_task(self, task_data: Dict[str, Any]) -> Dict[str, float]:
        """Adapt agent to new task"""
        pass
    
    @abstractmethod
    def meta_update(self, task_batch: List[Dict[str, Any]]) -> Dict[str, float]:
        """Meta-update across multiple tasks"""
        pass
    
    def update_task_context(self, observation: np.ndarray, reward: float, 
                          done: bool, info: Dict[str, Any]):
        """Update task context with new experience"""
        context = {
            "observation": observation,
            "reward": reward,
            "done": done,
            "info": info,
            "step": self.training_step
        }
        
        self.task_history.append(context)
        
        # Keep only recent history
        max_history = self.config.get("max_task_history", 1000)
        if len(self.task_history) > max_history:
            self.task_history = self.task_history[-max_history:]
    
    def get_meta_metrics(self) -> Dict[str, float]:
        """Get meta-learning metrics"""
        return self.meta_metrics.copy()

class NetworkUtils:
    """Utility functions for neural networks"""
    
    @staticmethod
    def create_mlp(input_dim: int, output_dim: int, hidden_dims: List[int], 
                   activation: nn.Module = nn.ReLU, dropout: float = 0.0) -> nn.Module:
        """Create multi-layer perceptron"""
        layers = []
        
        # Input layer
        layers.append(nn.Linear(input_dim, hidden_dims[0]))
        layers.append(activation())
        if dropout > 0:
            layers.append(nn.Dropout(dropout))
        
        # Hidden layers
        for i in range(len(hidden_dims) - 1):
            layers.append(nn.Linear(hidden_dims[i], hidden_dims[i + 1]))
            layers.append(activation())
            if dropout > 0:
                layers.append(nn.Dropout(dropout))
        
        # Output layer
        layers.append(nn.Linear(hidden_dims[-1], output_dim))
        
        return nn.Sequential(*layers)
    
    @staticmethod
    def init_weights(module: nn.Module, gain: float = 1.0):
        """Initialize network weights"""
        if isinstance(module, (nn.Linear, nn.Conv2d)):
            nn.init.orthogonal_(module.weight, gain=gain)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    @staticmethod
    def get_obs_shape(observation_space: spaces.Space) -> Tuple[int, ...]:
        """Get observation shape from space"""
        if isinstance(observation_space, spaces.Box):
            return observation_space.shape
        elif isinstance(observation_space, spaces.Discrete):
            return (observation_space.n,)
        else:
            raise NotImplementedError(f"Observation space {type(observation_space)} not supported")
    
    @staticmethod
    def get_action_dim(action_space: spaces.Space) -> int:
        """Get action dimension from space"""
        if isinstance(action_space, spaces.Box):
            return action_space.shape[0]
        elif isinstance(action_space, spaces.Discrete):
            return action_space.n
        else:
            raise NotImplementedError(f"Action space {type(action_space)} not supported")
