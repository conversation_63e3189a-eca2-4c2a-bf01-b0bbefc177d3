"""
Proximal Policy Optimization (PPO) Agent
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, Any, Tuple, List
from gymnasium import spaces
import logging

from .base_agent import BaseAgent, NetworkUtils

logger = logging.getLogger(__name__)

class ActorCritic(nn.Module):
    """Actor-Critic network for PPO"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 hidden_dims: List[int] = [64, 64], activation: nn.Module = nn.Tanh):
        super().__init__()
        
        self.observation_space = observation_space
        self.action_space = action_space
        
        # Get dimensions
        obs_shape = NetworkUtils.get_obs_shape(observation_space)
        if len(obs_shape) == 1:
            obs_dim = obs_shape[0]
        else:
            # For image observations, flatten
            obs_dim = np.prod(obs_shape)
        
        # Shared feature extractor
        self.feature_extractor = NetworkUtils.create_mlp(
            obs_dim, hidden_dims[-1], hidden_dims[:-1], activation
        )
        
        # Actor head
        if isinstance(action_space, spaces.Box):
            # Continuous actions
            action_dim = action_space.shape[0]
            self.actor_mean = nn.Linear(hidden_dims[-1], action_dim)
            self.actor_logstd = nn.Parameter(torch.zeros(action_dim))
            self.continuous = True
        elif isinstance(action_space, spaces.Discrete):
            # Discrete actions
            action_dim = action_space.n
            self.actor_head = nn.Linear(hidden_dims[-1], action_dim)
            self.continuous = False
        else:
            raise NotImplementedError(f"Action space {type(action_space)} not supported")
        
        # Critic head
        self.critic_head = nn.Linear(hidden_dims[-1], 1)
        
        # Initialize weights
        self.apply(lambda m: NetworkUtils.init_weights(m, gain=np.sqrt(2)))
        
        # Special initialization for policy head
        if self.continuous:
            NetworkUtils.init_weights(self.actor_mean, gain=0.01)
        else:
            NetworkUtils.init_weights(self.actor_head, gain=0.01)
        
        NetworkUtils.init_weights(self.critic_head, gain=1.0)
    
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Forward pass returning action distribution and value"""
        # Flatten observation if needed
        if obs.dim() > 2:
            obs = obs.view(obs.size(0), -1)
        
        features = self.feature_extractor(obs)
        
        # Actor
        if self.continuous:
            action_mean = self.actor_mean(features)
            action_std = torch.exp(self.actor_logstd)
            action_dist = Normal(action_mean, action_std)
        else:
            action_logits = self.actor_head(features)
            action_dist = Categorical(logits=action_logits)
        
        # Critic
        value = self.critic_head(features)
        
        return action_dist, value.squeeze(-1)
    
    def get_value(self, obs: torch.Tensor) -> torch.Tensor:
        """Get value estimate"""
        if obs.dim() > 2:
            obs = obs.view(obs.size(0), -1)
        
        features = self.feature_extractor(obs)
        value = self.critic_head(features)
        return value.squeeze(-1)

class PPOAgent(BaseAgent):
    """Proximal Policy Optimization Agent"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space,
                 config: Dict[str, Any], device: torch.device):
        # Set hyperparameters before calling super().__init__
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.batch_size = config.get("batch_size", 64)
        self.n_epochs = config.get("n_epochs", 10)
        self.gamma = config.get("gamma", 0.99)
        self.gae_lambda = config.get("gae_lambda", 0.95)
        self.clip_range = config.get("clip_range", 0.2)
        self.ent_coef = config.get("ent_coef", 0.01)
        self.vf_coef = config.get("vf_coef", 0.5)
        self.max_grad_norm = config.get("max_grad_norm", 0.5)

        super().__init__(observation_space, action_space, config, device)
        
        # Buffer for storing experiences
        self.buffer_size = config.get("buffer_size", 2048)
        self.buffer = {
            "observations": [],
            "actions": [],
            "rewards": [],
            "values": [],
            "log_probs": [],
            "dones": [],
            "advantages": [],
            "returns": [],
        }
        
        # Training metrics
        self.training_metrics = {
            "policy_loss": 0.0,
            "value_loss": 0.0,
            "entropy_loss": 0.0,
            "total_loss": 0.0,
            "kl_divergence": 0.0,
            "clip_fraction": 0.0,
        }
    
    def _build_networks(self):
        """Build actor-critic network"""
        hidden_dims = self.config.get("hidden_dims", [64, 64])
        activation = getattr(nn, self.config.get("activation", "Tanh"))
        
        self.actor_critic = ActorCritic(
            self.observation_space,
            self.action_space,
            hidden_dims,
            activation
        ).to(self.device)
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.actor_critic.parameters(),
            lr=self.learning_rate,
            eps=1e-5
        )
        
        logger.info(f"Built PPO networks with {sum(p.numel() for p in self.actor_critic.parameters())} parameters")
    
    def select_action(self, observation: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """Select action given observation"""
        with torch.no_grad():
            obs_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
            action_dist, value = self.actor_critic(obs_tensor)
            
            if deterministic:
                if self.actor_critic.continuous:
                    action = action_dist.mean
                else:
                    action = action_dist.probs.argmax(dim=-1)
            else:
                action = action_dist.sample()
            
            log_prob = action_dist.log_prob(action)
            
            # Store for training
            if not deterministic:
                self.buffer["observations"].append(observation)
                self.buffer["actions"].append(action.cpu().numpy())
                self.buffer["values"].append(value.cpu().numpy())
                self.buffer["log_probs"].append(log_prob.cpu().numpy())
            
            return action.cpu().numpy().squeeze()
    
    def store_transition(self, reward: float, done: bool, info: Dict[str, Any] = None):
        """Store transition in buffer"""
        self.buffer["rewards"].append(reward)
        self.buffer["dones"].append(done)
    
    def compute_gae(self, rewards: List[float], values: List[float], 
                   dones: List[bool], next_value: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """Compute Generalized Advantage Estimation"""
        advantages = []
        gae = 0
        
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = values[i + 1]
            
            delta = rewards[i] + self.gamma * next_value_i * next_non_terminal - values[i]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)
        
        advantages = np.array(advantages)
        returns = advantages + np.array(values)
        
        return advantages, returns
    
    def update(self, batch: Dict[str, Any] = None) -> Dict[str, float]:
        """Update agent with collected experiences"""
        if len(self.buffer["observations"]) < self.batch_size:
            return self.training_metrics
        
        # Compute advantages and returns
        advantages, returns = self.compute_gae(
            self.buffer["rewards"],
            self.buffer["values"],
            self.buffer["dones"]
        )
        
        self.buffer["advantages"] = advantages.tolist()
        self.buffer["returns"] = returns.tolist()
        
        # Convert to tensors
        observations = torch.FloatTensor(np.array(self.buffer["observations"])).to(self.device)
        actions = torch.FloatTensor(np.array(self.buffer["actions"])).to(self.device)
        old_log_probs = torch.FloatTensor(np.array(self.buffer["log_probs"])).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO update
        total_policy_loss = 0
        total_value_loss = 0
        total_entropy_loss = 0
        total_kl_div = 0
        total_clip_fraction = 0
        
        for epoch in range(self.n_epochs):
            # Mini-batch updates
            indices = torch.randperm(len(observations))
            
            for start in range(0, len(observations), self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]
                
                batch_obs = observations[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # Forward pass
                action_dist, values = self.actor_critic(batch_obs)
                new_log_probs = action_dist.log_prob(batch_actions)
                entropy = action_dist.entropy().mean()
                
                # Policy loss
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # Value loss
                value_loss = F.mse_loss(values, batch_returns)
                
                # Total loss
                loss = policy_loss + self.vf_coef * value_loss - self.ent_coef * entropy
                
                # Backward pass
                self.optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.actor_critic.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # Metrics
                with torch.no_grad():
                    kl_div = (batch_old_log_probs - new_log_probs).mean()
                    clip_fraction = ((ratio - 1.0).abs() > self.clip_range).float().mean()
                
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_entropy_loss += entropy.item()
                total_kl_div += kl_div.item()
                total_clip_fraction += clip_fraction.item()
        
        # Update metrics
        n_updates = self.n_epochs * (len(observations) // self.batch_size)
        self.training_metrics.update({
            "policy_loss": total_policy_loss / n_updates,
            "value_loss": total_value_loss / n_updates,
            "entropy_loss": total_entropy_loss / n_updates,
            "total_loss": (total_policy_loss + total_value_loss - total_entropy_loss) / n_updates,
            "kl_divergence": total_kl_div / n_updates,
            "clip_fraction": total_clip_fraction / n_updates,
        })
        
        # Clear buffer
        self.buffer = {key: [] for key in self.buffer.keys()}
        self.training_step += 1
        
        return self.training_metrics
    
    def save(self, path: str):
        """Save agent state"""
        torch.save({
            "actor_critic_state_dict": self.actor_critic.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "training_step": self.training_step,
            "config": self.config,
        }, path)
        logger.info(f"Saved PPO agent to {path}")
    
    def load(self, path: str):
        """Load agent state"""
        checkpoint = torch.load(path, map_location=self.device)
        self.actor_critic.load_state_dict(checkpoint["actor_critic_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.training_step = checkpoint["training_step"]
        logger.info(f"Loaded PPO agent from {path}")
