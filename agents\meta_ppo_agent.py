"""
Meta-Learning PPO Agent with Adaptive Safety Constraints
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, Any, Tuple, List
from gymnasium import spaces
import logging

from .ppo_agent import PPOAgent, ActorCritic
from .base_agent import MetaLearningAgent, NetworkUtils

logger = logging.getLogger(__name__)

class MetaLearner(nn.Module):
    """Meta-learner network for adaptive safety constraints"""
    
    def __init__(self, context_dim: int, hidden_dim: int = 128, output_dim: int = 64):
        super().__init__()
        
        self.context_encoder = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
        
        # Safety constraint predictor
        self.safety_predictor = nn.Sequential(
            nn.Linear(output_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # Output between 0 and 1
        )
        
        # Risk assessment
        self.risk_assessor = nn.Sequential(
            nn.Linear(output_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # Apply weight initialization
        self.apply(lambda m: NetworkUtils.init_weights(m, gain=np.sqrt(2)))
    
    def forward(self, context: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass through meta-learner"""
        encoded_context = self.context_encoder(context)
        safety_constraint = self.safety_predictor(encoded_context)
        risk_level = self.risk_assessor(encoded_context)
        
        return encoded_context, safety_constraint, risk_level

class MetaActorCritic(ActorCritic):
    """Actor-Critic with meta-learning context integration"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space,
                 context_dim: int = 64, hidden_dims: List[int] = [64, 64], 
                 activation: nn.Module = nn.Tanh):
        super().__init__(observation_space, action_space, hidden_dims, activation)
        
        self.context_dim = context_dim
        
        # Modify feature extractor to include context
        obs_shape = NetworkUtils.get_obs_shape(observation_space)
        if len(obs_shape) == 1:
            obs_dim = obs_shape[0]
        else:
            obs_dim = np.prod(obs_shape)
        
        # Context integration layer
        self.context_integration = nn.Linear(context_dim, hidden_dims[0])
        
        # Rebuild feature extractor with context
        self.feature_extractor = NetworkUtils.create_mlp(
            obs_dim + hidden_dims[0], hidden_dims[-1], hidden_dims[:-1], activation
        )
        
        # Safety-aware value head
        self.safety_critic = nn.Linear(hidden_dims[-1], 1)
        
        # Re-initialize weights
        self.apply(lambda m: NetworkUtils.init_weights(m, gain=np.sqrt(2)))
    
    def forward(self, obs: torch.Tensor, context: torch.Tensor = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass with context integration"""
        # Flatten observation if needed
        if obs.dim() > 2:
            obs = obs.view(obs.size(0), -1)
        
        # Integrate context if provided
        if context is not None:
            context_features = self.context_integration(context)
            combined_input = torch.cat([obs, context_features], dim=-1)
        else:
            # Use zero context if not provided
            batch_size = obs.size(0)
            zero_context = torch.zeros(batch_size, self.context_dim, device=obs.device)
            context_features = self.context_integration(zero_context)
            combined_input = torch.cat([obs, context_features], dim=-1)
        
        features = self.feature_extractor(combined_input)
        
        # Actor
        if self.continuous:
            action_mean = self.actor_mean(features)
            action_std = torch.exp(self.actor_logstd)
            action_dist = Normal(action_mean, action_std)
        else:
            action_logits = self.actor_head(features)
            action_dist = Categorical(logits=action_logits)
        
        # Critics
        value = self.critic_head(features)
        safety_value = self.safety_critic(features)
        
        return action_dist, value.squeeze(-1), safety_value.squeeze(-1)

class MetaPPOAgent(MetaLearningAgent):
    """Meta-Learning PPO Agent with Adaptive Safety"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space,
                 config: Dict[str, Any], device: torch.device):
        # Set hyperparameters before calling super().__init__
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.batch_size = config.get("batch_size", 64)
        self.n_epochs = config.get("n_epochs", 10)
        self.gamma = config.get("gamma", 0.99)
        self.gae_lambda = config.get("gae_lambda", 0.95)
        self.clip_range = config.get("clip_range", 0.2)
        self.ent_coef = config.get("ent_coef", 0.01)
        self.vf_coef = config.get("vf_coef", 0.5)
        self.max_grad_norm = config.get("max_grad_norm", 0.5)

        # Meta-learning specific parameters (needed before super().__init__)
        self.context_dim = config.get("context_dim", 64)
        self.context_window = config.get("context_window", 50)
        self.meta_learning_rate = config.get("meta_learning_rate", 1e-4)
        self.meta_batch_size = config.get("meta_batch_size", 32)
        self.adaptation_steps = config.get("adaptation_steps", 5)

        super().__init__(observation_space, action_space, config, device)
        
        # PPO parameters (inherited from base PPO)
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.batch_size = config.get("batch_size", 64)
        self.n_epochs = config.get("n_epochs", 10)
        self.gamma = config.get("gamma", 0.99)
        self.gae_lambda = config.get("gae_lambda", 0.95)
        self.clip_range = config.get("clip_range", 0.2)
        self.ent_coef = config.get("ent_coef", 0.01)
        self.vf_coef = config.get("vf_coef", 0.5)
        self.max_grad_norm = config.get("max_grad_norm", 0.5)
        
        # Context buffer for meta-learning
        self.context_buffer = []
        self.current_context = None
        
        # Buffer for storing experiences
        self.buffer_size = config.get("buffer_size", 2048)
        self.buffer = {
            "observations": [],
            "actions": [],
            "rewards": [],
            "values": [],
            "safety_values": [],
            "log_probs": [],
            "dones": [],
            "contexts": [],
            "safety_constraints": [],
            "risk_levels": [],
            "advantages": [],
            "returns": [],
            "safety_returns": [],
        }
    
    def _build_networks(self):
        """Build meta-learning networks"""
        hidden_dims = self.config.get("hidden_dims", [64, 64])
        activation = getattr(nn, self.config.get("activation", "Tanh"))
        
        # Meta-learner
        context_input_dim = self._get_context_input_dim()
        self.meta_learner = MetaLearner(
            context_dim=context_input_dim,
            hidden_dim=128,
            output_dim=self.context_dim
        ).to(self.device)
        
        # Meta Actor-Critic
        self.actor_critic = MetaActorCritic(
            self.observation_space,
            self.action_space,
            self.context_dim,
            hidden_dims,
            activation
        ).to(self.device)
        
        # Optimizers
        self.optimizer = optim.Adam(
            self.actor_critic.parameters(),
            lr=self.learning_rate,
            eps=1e-5
        )
        
        self.meta_optimizer = optim.Adam(
            self.meta_learner.parameters(),
            lr=self.meta_learning_rate,
            eps=1e-5
        )
        
        logger.info(f"Built Meta-PPO networks with {sum(p.numel() for p in self.actor_critic.parameters())} + "
                   f"{sum(p.numel() for p in self.meta_learner.parameters())} parameters")
    
    def _get_context_input_dim(self) -> int:
        """Get dimension of context input"""
        # Context includes: recent rewards, safety violations, episode statistics
        return 10  # Configurable based on context features
    
    def _extract_context_features(self) -> torch.Tensor:
        """Extract context features from recent experience"""
        if len(self.task_history) < 5:
            # Not enough history, return zero context
            return torch.zeros(1, self._get_context_input_dim(), device=self.device)
        
        # Extract features from recent history
        recent_history = self.task_history[-self.context_window:]
        
        features = []
        
        # Recent rewards statistics
        rewards = [h["reward"] for h in recent_history]
        features.extend([
            np.mean(rewards),
            np.std(rewards),
            np.min(rewards),
            np.max(rewards)
        ])
        
        # Safety violation statistics
        violations = [h["info"].get("safety_violation", 0) for h in recent_history]
        features.extend([
            np.mean(violations),
            np.sum(violations),
        ])
        
        # Episode statistics
        episode_lengths = []
        current_length = 0
        for h in recent_history:
            current_length += 1
            if h["done"]:
                episode_lengths.append(current_length)
                current_length = 0
        
        if episode_lengths:
            features.extend([
                np.mean(episode_lengths),
                np.std(episode_lengths) if len(episode_lengths) > 1 else 0.0
            ])
        else:
            features.extend([current_length, 0.0])
        
        # Recent performance trend
        if len(rewards) >= 10:
            recent_trend = np.mean(rewards[-5:]) - np.mean(rewards[-10:-5])
            features.append(recent_trend)
        else:
            features.append(0.0)
        
        # Constraint satisfaction rate
        constraint_violations = [h["info"].get("constraint_violation", 0) for h in recent_history]
        satisfaction_rate = 1.0 - np.mean(constraint_violations)
        features.append(satisfaction_rate)
        
        return torch.FloatTensor(features).unsqueeze(0).to(self.device)
    
    def select_action(self, observation: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """Select action with meta-learning context"""
        with torch.no_grad():
            obs_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
            
            # Extract context
            context_features = self._extract_context_features()
            encoded_context, safety_constraint, risk_level = self.meta_learner(context_features)
            
            # Get action from actor-critic
            action_dist, value, safety_value = self.actor_critic(obs_tensor, encoded_context)
            
            if deterministic:
                if self.actor_critic.continuous:
                    action = action_dist.mean
                else:
                    action = action_dist.probs.argmax(dim=-1)
            else:
                action = action_dist.sample()
            
            log_prob = action_dist.log_prob(action)
            
            # Store for training
            if not deterministic:
                self.buffer["observations"].append(observation)
                self.buffer["actions"].append(action.cpu().numpy())
                self.buffer["values"].append(value.cpu().numpy())
                self.buffer["safety_values"].append(safety_value.cpu().numpy())
                self.buffer["log_probs"].append(log_prob.cpu().numpy())
                self.buffer["contexts"].append(encoded_context.cpu().numpy())
                self.buffer["safety_constraints"].append(safety_constraint.cpu().numpy())
                self.buffer["risk_levels"].append(risk_level.cpu().numpy())
            
            # Update current context
            self.current_context = {
                "encoded_context": encoded_context,
                "safety_constraint": safety_constraint.item(),
                "risk_level": risk_level.item()
            }
            
            return action.cpu().numpy().squeeze()
    
    def store_transition(self, reward: float, done: bool):
        """Store transition in buffer"""
        self.buffer["rewards"].append(reward)
        self.buffer["dones"].append(done)
    
    def adapt_to_task(self, task_data: Dict[str, Any]) -> Dict[str, float]:
        """Adapt agent to new task using meta-learning"""
        # Extract task-specific context
        context_features = self._extract_context_features()
        
        # Fast adaptation using gradient descent
        adaptation_loss = 0.0
        
        with torch.enable_grad():
            # Clone parameters for adaptation
            adapted_params = {}
            for name, param in self.actor_critic.named_parameters():
                adapted_params[name] = param.clone().requires_grad_(True)
            
            # Perform adaptation steps
            for step in range(self.adaptation_steps):
                # Forward pass with adapted parameters
                # (Simplified - in practice would use higher-order gradients)
                encoded_context, safety_constraint, risk_level = self.meta_learner(context_features)
                
                # Compute adaptation loss based on task data
                # This would typically involve a small batch of task-specific data
                loss = torch.tensor(0.0, device=self.device)  # Placeholder
                
                # Update adapted parameters
                grads = torch.autograd.grad(loss, adapted_params.values(), create_graph=True)
                for (name, param), grad in zip(adapted_params.items(), grads):
                    adapted_params[name] = param - self.meta_learning_rate * grad
                
                adaptation_loss += loss.item()
        
        self.meta_metrics["adaptation_loss"] = adaptation_loss / self.adaptation_steps
        return self.meta_metrics
    
    def meta_update(self, task_batch: List[Dict[str, Any]]) -> Dict[str, float]:
        """Meta-update across multiple tasks"""
        meta_loss = 0.0
        
        for task_data in task_batch:
            # Perform task adaptation
            adaptation_metrics = self.adapt_to_task(task_data)
            meta_loss += adaptation_metrics["adaptation_loss"]
        
        # Update meta-learner
        self.meta_optimizer.zero_grad()
        meta_loss_tensor = torch.tensor(meta_loss / len(task_batch), device=self.device, requires_grad=True)
        meta_loss_tensor.backward()
        torch.nn.utils.clip_grad_norm_(self.meta_learner.parameters(), self.max_grad_norm)
        self.meta_optimizer.step()
        
        self.meta_metrics["meta_loss"] = meta_loss / len(task_batch)
        return self.meta_metrics

    def compute_safety_loss(self, batch: Dict[str, Any]) -> torch.Tensor:
        """Compute safety-related loss for meta-learning"""
        # Extract safety-related data from batch
        safety_constraints = batch.get("safety_constraints", torch.zeros(len(batch["rewards"]), device=self.device))
        risk_levels = batch.get("risk_levels", torch.zeros(len(batch["rewards"]), device=self.device))

        # Safety constraint prediction loss
        target_constraints = batch.get("target_constraints", safety_constraints)
        constraint_loss = torch.nn.functional.mse_loss(safety_constraints, target_constraints)

        # Risk level prediction loss
        target_risks = batch.get("target_risks", risk_levels)
        risk_loss = torch.nn.functional.mse_loss(risk_levels, target_risks)

        # Combined safety loss
        safety_loss = constraint_loss + risk_loss

        return safety_loss
    
    def update(self, batch: Dict[str, Any] = None) -> Dict[str, float]:
        """Update agent with meta-learning"""
        if len(self.buffer["observations"]) < self.batch_size:
            return {}
        
        # Standard PPO update with safety considerations
        # (Implementation similar to PPO but with safety values and constraints)
        
        # Compute advantages for both reward and safety
        advantages, returns = self._compute_gae(
            self.buffer["rewards"],
            self.buffer["values"],
            self.buffer["dones"]
        )
        
        safety_advantages, safety_returns = self._compute_gae(
            [info.get("safety_cost", 0) for info in self.buffer.get("infos", [{}] * len(self.buffer["rewards"]))],
            self.buffer["safety_values"],
            self.buffer["dones"]
        )
        
        # Update networks (simplified)
        training_metrics = self._ppo_update(advantages, returns, safety_advantages, safety_returns)
        
        # Meta-learning update
        if len(self.task_history) > self.context_window:
            meta_metrics = self._meta_update()
            training_metrics.update(meta_metrics)
        
        # Clear buffer
        self.buffer = {key: [] for key in self.buffer.keys()}
        self.training_step += 1
        
        return training_metrics
    
    def _compute_gae(self, rewards: List[float], values: List[float], 
                    dones: List[bool], next_value: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """Compute Generalized Advantage Estimation"""
        advantages = []
        gae = 0
        
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = values[i + 1]
            
            delta = rewards[i] + self.gamma * next_value_i * next_non_terminal - values[i]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)
        
        advantages = np.array(advantages)
        returns = advantages + np.array(values)
        
        return advantages, returns
    
    def _ppo_update(self, advantages, returns, safety_advantages, safety_returns) -> Dict[str, float]:
        """PPO update with safety considerations"""
        # Simplified implementation - would include full PPO update logic
        return {
            "policy_loss": 0.0,
            "value_loss": 0.0,
            "safety_loss": 0.0,
            "meta_loss": 0.0
        }
    
    def _meta_update(self) -> Dict[str, float]:
        """Meta-learning update"""
        # Simplified implementation
        return {
            "meta_adaptation_loss": 0.0,
            "context_prediction_loss": 0.0
        }
    
    def save(self, path: str):
        """Save agent state"""
        torch.save({
            "actor_critic_state_dict": self.actor_critic.state_dict(),
            "meta_learner_state_dict": self.meta_learner.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "meta_optimizer_state_dict": self.meta_optimizer.state_dict(),
            "training_step": self.training_step,
            "config": self.config,
        }, path)
        logger.info(f"Saved Meta-PPO agent to {path}")
    
    def load(self, path: str):
        """Load agent state"""
        checkpoint = torch.load(path, map_location=self.device)
        self.actor_critic.load_state_dict(checkpoint["actor_critic_state_dict"])
        self.meta_learner.load_state_dict(checkpoint["meta_learner_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.meta_optimizer.load_state_dict(checkpoint["meta_optimizer_state_dict"])
        self.training_step = checkpoint["training_step"]
        logger.info(f"Loaded Meta-PPO agent from {path}")
