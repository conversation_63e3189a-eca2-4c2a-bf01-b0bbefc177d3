2025-08-13 18:57:07,667 - safe_rl_main - INFO - Starting experiment: safe_ppo_cartpole_safe_42
2025-08-13 18:57:07,670 - safe_rl_main - INFO - Created environment: cartpole_safe
2025-08-13 18:57:09,370 - safe_rl_main - INFO - Created agent: safe_ppo
2025-08-13 18:57:13,494 - safe_rl_main - ERROR - Experiment failed: Trying to backward through the graph a second time (or directly access saved tensors after they have already been freed). Saved intermediate values of the graph are freed when you call .backward() or autograd.grad(). Specify retain_graph=True if you need to backward through the graph a second time or if you need to access saved tensors after calling backward.
