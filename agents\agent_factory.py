"""
Agent Factory for creating different types of RL agents
"""

from typing import Dict, Any
import torch
from gymnasium import spaces
import logging

from .base_agent import BaseAgent
from .ppo_agent import PPOAgent
from .meta_ppo_agent import MetaPPOAgent
from .safe_ppo_agent import SafePPOAgent
from .transformer_ppo_agent import TransformerPPOAgent

logger = logging.getLogger(__name__)

class AgentFactory:
    """Factory for creating RL agents"""
    
    def __init__(self):
        self.agent_registry = {
            "ppo": PPOAgent,
            "meta_ppo": MetaPPOAgent,
            "safe_ppo": SafePPOAgent,
            "transformer_ppo": TransformerPPOAgent,
        }
    
    def create_agent(self, agent_type: str, env, config: Dict[str, Any]) -> BaseAgent:
        """Create agent of specified type"""
        
        if agent_type not in self.agent_registry:
            raise ValueError(f"Unknown agent type: {agent_type}. "
                           f"Available types: {list(self.agent_registry.keys())}")
        
        # Get environment spaces
        observation_space = env.observation_space
        action_space = env.action_space
        
        # Get device
        device = config.get("device", torch.device("cpu"))
        
        # Get agent config
        agent_config = config.get("agent", {})
        
        # Merge with global config
        full_config = {**config, **agent_config}
        
        # Create agent
        agent_class = self.agent_registry[agent_type]
        agent = agent_class(
            observation_space=observation_space,
            action_space=action_space,
            config=full_config,
            device=device
        )
        
        logger.info(f"Created {agent_type} agent")
        logger.debug(f"Observation space: {observation_space}")
        logger.debug(f"Action space: {action_space}")
        logger.debug(f"Device: {device}")
        
        return agent
    
    def register_agent(self, name: str, agent_class: type):
        """Register new agent type"""
        self.agent_registry[name] = agent_class
        logger.info(f"Registered agent type: {name}")
    
    def list_agents(self) -> list:
        """List available agent types"""
        return list(self.agent_registry.keys())
    
    def get_agent_info(self, agent_type: str) -> Dict[str, Any]:
        """Get information about agent type"""
        if agent_type not in self.agent_registry:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        agent_class = self.agent_registry[agent_type]
        
        return {
            "name": agent_type,
            "class": agent_class.__name__,
            "module": agent_class.__module__,
            "docstring": agent_class.__doc__,
        }
