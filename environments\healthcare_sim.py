"""
Healthcare Simulation Environment for Safe RL
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class HealthcareSimEnv(gym.Env):
    """
    Healthcare simulation environment for drug dosage optimization
    
    Safety constraints:
    - Maximum safe dosage limits
    - Minimum effective dosage
    - Patient vital signs monitoring
    - Drug interaction constraints
    """
    
    metadata = {"render_modes": ["human"], "render_fps": 1}
    
    def __init__(self, 
                 max_episode_steps: int = 100,
                 safety_budget: float = 0.05,
                 dose_limit: float = 10.0,
                 min_effective_dose: float = 1.0,
                 patient_sensitivity: float = 1.0,
                 constraint_type: str = "hard",
                 render_mode: Optional[str] = None,
                 **kwargs):
        
        super().__init__()
        
        # Environment parameters
        self.max_episode_steps = max_episode_steps
        self.safety_budget = safety_budget
        self.dose_limit = dose_limit
        self.min_effective_dose = min_effective_dose
        self.patient_sensitivity = patient_sensitivity
        self.constraint_type = constraint_type
        self.render_mode = render_mode
        
        # Patient parameters
        self.baseline_health = 50.0  # Baseline health score
        self.disease_severity = np.random.uniform(0.5, 2.0)  # Disease progression rate
        self.drug_effectiveness = np.random.uniform(0.8, 1.2)  # Drug effectiveness
        self.toxicity_threshold = dose_limit * 0.8  # Toxicity threshold
        
        # Action space: continuous dosage amount
        self.action_space = spaces.Box(
            low=0.0, high=dose_limit, shape=(1,), dtype=np.float32
        )
        
        # Observation space: [health_score, disease_progress, drug_concentration, 
        #                    vital_signs..., safety_indicators...]
        self.observation_space = spaces.Box(
            low=0.0, high=100.0, shape=(10,), dtype=np.float32
        )
        
        # State variables
        self.current_step = 0
        self.health_score = self.baseline_health
        self.disease_progress = 0.0
        self.drug_concentration = 0.0
        self.cumulative_dose = 0.0
        
        # Vital signs
        self.heart_rate = 70.0
        self.blood_pressure = 120.0
        self.temperature = 98.6
        self.oxygen_saturation = 98.0
        
        # Safety tracking
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        self.toxicity_events = 0
        
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset environment to initial state"""
        super().reset(seed=seed)
        
        # Reset patient parameters
        self.disease_severity = self.np_random.uniform(0.5, 2.0)
        self.drug_effectiveness = self.np_random.uniform(0.8, 1.2)
        self.patient_sensitivity = self.np_random.uniform(0.5, 1.5)
        
        # Reset state
        self.current_step = 0
        self.health_score = self.baseline_health
        self.disease_progress = 0.0
        self.drug_concentration = 0.0
        self.cumulative_dose = 0.0
        
        # Reset vital signs
        self.heart_rate = self.np_random.normal(70.0, 5.0)
        self.blood_pressure = self.np_random.normal(120.0, 10.0)
        self.temperature = self.np_random.normal(98.6, 0.5)
        self.oxygen_saturation = self.np_random.normal(98.0, 1.0)
        
        # Reset safety tracking
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        self.toxicity_events = 0
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Execute one step in the environment"""
        dose = np.clip(action[0], 0.0, self.dose_limit)
        self.cumulative_dose += dose
        
        # Update drug concentration (simple pharmacokinetics)
        # Drug concentration increases with dose and decreases with metabolism
        metabolism_rate = 0.1
        self.drug_concentration = (self.drug_concentration * (1 - metabolism_rate) + 
                                 dose * self.drug_effectiveness)
        
        # Disease progression
        disease_effect = self.disease_severity * 0.5
        drug_benefit = min(self.drug_concentration * 2.0, 10.0)  # Diminishing returns
        
        # Update health score
        health_change = drug_benefit - disease_effect
        self.health_score = np.clip(self.health_score + health_change, 0.0, 100.0)
        
        # Update disease progress
        self.disease_progress += self.disease_severity * 0.1
        
        # Update vital signs based on drug effects and disease
        self._update_vital_signs(dose)
        
        self.current_step += 1
        
        # Check safety constraints
        safety_violation, constraint_violation, cost = self._check_safety_constraints(dose)
        
        # Compute reward
        reward = self._compute_reward(dose, safety_violation, constraint_violation)
        
        # Update safety tracking
        if safety_violation:
            self.safety_violations += 1
        if constraint_violation:
            self.constraint_violations += 1
        self.cumulative_cost += cost
        
        # Check termination conditions
        terminated = (self.health_score <= 0 or  # Patient died
                     self.health_score >= 95 or  # Patient fully recovered
                     self.toxicity_events >= 3)   # Too many toxicity events
        
        truncated = self.current_step >= self.max_episode_steps
        
        # Get observation and info
        observation = self._get_observation()
        info = self._get_info()
        info.update({
            "safety_violation": safety_violation,
            "constraint_violation": constraint_violation,
            "cost": cost,
            "cumulative_cost": self.cumulative_cost,
            "dose_given": dose,
            "toxicity_event": dose > self.toxicity_threshold,
        })
        
        return observation, reward, terminated, truncated, info
    
    def _update_vital_signs(self, dose: float):
        """Update patient vital signs based on drug dose"""
        # Heart rate increases with high doses
        if dose > self.toxicity_threshold:
            self.heart_rate += self.np_random.normal(5.0, 2.0)
        else:
            self.heart_rate += self.np_random.normal(0.0, 1.0)
        
        # Blood pressure affected by drug concentration
        bp_effect = (self.drug_concentration - 5.0) * 2.0
        self.blood_pressure += bp_effect + self.np_random.normal(0.0, 2.0)
        
        # Temperature increases with toxicity
        if dose > self.toxicity_threshold:
            self.temperature += self.np_random.normal(0.5, 0.2)
        else:
            self.temperature += self.np_random.normal(0.0, 0.1)
        
        # Oxygen saturation decreases with severe toxicity
        if dose > self.dose_limit * 0.9:
            self.oxygen_saturation -= self.np_random.uniform(1.0, 3.0)
        
        # Clamp vital signs to realistic ranges
        self.heart_rate = np.clip(self.heart_rate, 40.0, 200.0)
        self.blood_pressure = np.clip(self.blood_pressure, 80.0, 200.0)
        self.temperature = np.clip(self.temperature, 95.0, 110.0)
        self.oxygen_saturation = np.clip(self.oxygen_saturation, 70.0, 100.0)
    
    def _check_safety_constraints(self, dose: float) -> Tuple[bool, bool, float]:
        """Check safety constraints and return violations and cost"""
        safety_violation = False
        constraint_violation = False
        cost = 0.0
        
        # Dosage limit constraint
        if dose > self.dose_limit:
            safety_violation = True
            cost += (dose - self.dose_limit) * 10.0
        
        # Toxicity threshold
        if dose > self.toxicity_threshold:
            constraint_violation = True
            cost += (dose - self.toxicity_threshold) * 5.0
            self.toxicity_events += 1
        
        # Vital signs constraints
        if self.heart_rate > 150 or self.heart_rate < 50:
            safety_violation = True
            cost += abs(self.heart_rate - 100) * 0.1
        
        if self.blood_pressure > 180 or self.blood_pressure < 90:
            safety_violation = True
            cost += abs(self.blood_pressure - 120) * 0.05
        
        if self.temperature > 102.0:
            safety_violation = True
            cost += (self.temperature - 102.0) * 2.0
        
        if self.oxygen_saturation < 90.0:
            safety_violation = True
            cost += (90.0 - self.oxygen_saturation) * 1.0
        
        # Cumulative dose constraint
        if self.cumulative_dose > self.dose_limit * self.max_episode_steps * 0.5:
            constraint_violation = True
            cost += 1.0
        
        return safety_violation, constraint_violation, cost
    
    def _compute_reward(self, dose: float, safety_violation: bool, constraint_violation: bool) -> float:
        """Compute reward based on health improvement and safety"""
        # Base reward from health improvement
        health_reward = (self.health_score - self.baseline_health) * 0.1
        
        # Penalty for disease progression
        disease_penalty = self.disease_progress * 0.05
        
        # Effectiveness reward (for giving appropriate dose)
        if dose >= self.min_effective_dose:
            effectiveness_reward = min(dose / self.min_effective_dose, 2.0)
        else:
            effectiveness_reward = dose / self.min_effective_dose - 1.0  # Penalty for under-dosing
        
        # Safety penalties
        safety_penalty = 0.0
        if safety_violation:
            safety_penalty += 20.0
        if constraint_violation:
            safety_penalty += 10.0
        
        # Toxicity penalty
        if dose > self.toxicity_threshold:
            safety_penalty += (dose - self.toxicity_threshold) * 5.0
        
        # Combine rewards
        total_reward = health_reward + effectiveness_reward - disease_penalty - safety_penalty
        
        # Bonus for maintaining patient stability
        if (50 <= self.heart_rate <= 100 and 
            90 <= self.blood_pressure <= 140 and 
            self.temperature <= 100.0 and 
            self.oxygen_saturation >= 95.0):
            total_reward += 2.0
        
        return total_reward
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation"""
        # Safety indicators
        dose_safety = 1.0 if self.cumulative_dose < self.dose_limit * self.max_episode_steps * 0.4 else 0.0
        vital_safety = 1.0 if (50 <= self.heart_rate <= 150 and 
                              90 <= self.blood_pressure <= 180 and 
                              self.temperature <= 102.0 and 
                              self.oxygen_saturation >= 90.0) else 0.0
        
        observation = np.array([
            self.health_score / 100.0,           # Normalized health score
            self.disease_progress / 10.0,        # Normalized disease progress
            self.drug_concentration / 10.0,      # Normalized drug concentration
            self.heart_rate / 200.0,             # Normalized heart rate
            self.blood_pressure / 200.0,         # Normalized blood pressure
            (self.temperature - 95.0) / 15.0,    # Normalized temperature
            self.oxygen_saturation / 100.0,      # Normalized oxygen saturation
            self.cumulative_dose / (self.dose_limit * self.max_episode_steps),  # Normalized cumulative dose
            dose_safety,                         # Dose safety indicator
            vital_safety,                        # Vital signs safety indicator
        ], dtype=np.float32)
        
        return observation
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info"""
        return {
            "step": self.current_step,
            "health_score": self.health_score,
            "disease_progress": self.disease_progress,
            "drug_concentration": self.drug_concentration,
            "cumulative_dose": self.cumulative_dose,
            "heart_rate": self.heart_rate,
            "blood_pressure": self.blood_pressure,
            "temperature": self.temperature,
            "oxygen_saturation": self.oxygen_saturation,
            "safety_violations": self.safety_violations,
            "constraint_violations": self.constraint_violations,
            "cumulative_cost": self.cumulative_cost,
            "toxicity_events": self.toxicity_events,
            "patient_sensitivity": self.patient_sensitivity,
            "disease_severity": self.disease_severity,
            "safety_budget_used": self.cumulative_cost / (self.safety_budget * self.max_episode_steps),
        }
    
    def render(self):
        """Render the environment"""
        if self.render_mode == "human":
            print(f"\n=== Healthcare Simulation Step {self.current_step} ===")
            print(f"Health Score: {self.health_score:.1f}/100")
            print(f"Disease Progress: {self.disease_progress:.2f}")
            print(f"Drug Concentration: {self.drug_concentration:.2f}")
            print(f"Cumulative Dose: {self.cumulative_dose:.2f}/{self.dose_limit * self.max_episode_steps:.1f}")
            print(f"Vital Signs:")
            print(f"  Heart Rate: {self.heart_rate:.1f} bpm")
            print(f"  Blood Pressure: {self.blood_pressure:.1f} mmHg")
            print(f"  Temperature: {self.temperature:.1f}°F")
            print(f"  Oxygen Saturation: {self.oxygen_saturation:.1f}%")
            print(f"Safety Violations: {self.safety_violations}")
            print(f"Constraint Violations: {self.constraint_violations}")
            print(f"Toxicity Events: {self.toxicity_events}")
    
    def close(self):
        """Close the environment"""
        pass
