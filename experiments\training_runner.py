"""
Training Runner for Safe RL Experiments
"""

import time
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

import torch
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from utils.logger import SafeRLLogger

logger = logging.getLogger(__name__)

class TrainingRunner:
    """Handles training loop for Safe RL agents"""
    
    def __init__(self, agent, env, config: Dict[str, Any], logger: SafeRLLogger):
        self.agent = agent
        self.env = env
        self.config = config
        self.logger = logger
        
        # Training parameters
        self.total_timesteps = config.get("total_timesteps", 100000)
        self.eval_freq = config.get("eval_freq", 5000)
        self.n_eval_episodes = config.get("n_eval_episodes", 10)
        self.save_freq = config.get("save_freq", 10000)
        self.log_freq = config.get("log_freq", 1000)
        
        # Early stopping
        self.early_stopping = config.get("early_stopping", False)
        self.patience = config.get("patience", 10)
        self.min_improvement = config.get("min_improvement", 0.01)
        
        # Results tracking
        self.training_results = {
            "timesteps": [],
            "episodes": [],
            "rewards": [],
            "episode_lengths": [],
            "safety_violations": [],
            "constraint_violations": [],
            "training_metrics": [],
            "evaluation_results": [],
        }
        
        # Best model tracking
        self.best_reward = -np.inf
        self.best_safety_score = -np.inf
        self.episodes_without_improvement = 0
        
        # Setup directories
        self.setup_directories()
    
    def setup_directories(self):
        """Setup result and model directories"""
        experiment_name = self.config.get("experiment_name", "default")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.experiment_dir = Path("results") / f"{experiment_name}_{timestamp}"
        self.model_dir = self.experiment_dir / "models"
        self.log_dir = self.experiment_dir / "logs"
        
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        self.model_dir.mkdir(exist_ok=True)
        self.log_dir.mkdir(exist_ok=True)
        
        # Save config
        import yaml
        with open(self.experiment_dir / "config.yaml", 'w') as f:
            yaml.dump(self.config, f, default_flow_style=False)
    
    def run(self) -> Dict[str, Any]:
        """Run training loop"""
        self.logger.log_experiment_start(self.config)
        
        start_time = time.time()
        timestep = 0
        episode = 0
        
        # Initialize environment
        obs, info = self.env.reset()
        self.agent.reset_episode()
        
        # Progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.logger.console
        ) as progress:
            
            task = progress.add_task("Training", total=self.total_timesteps)
            
            while timestep < self.total_timesteps:
                # Select and execute action
                action = self.agent.select_action(obs, deterministic=False)
                next_obs, reward, terminated, truncated, info = self.env.step(action)
                done = terminated or truncated
                
                # Store transition
                if hasattr(self.agent, 'store_transition'):
                    self.agent.store_transition(reward, done, info)
                
                # Update agent metrics
                self.agent.step(reward, done, info)
                
                # Update task context for meta-learning agents
                if hasattr(self.agent, 'update_task_context'):
                    self.agent.update_task_context(obs, reward, done, info)
                
                timestep += 1
                obs = next_obs
                
                # Episode end
                if done:
                    episode += 1
                    
                    # Get episode metrics
                    episode_metrics = self.agent.get_metrics()
                    
                    # Log episode results
                    if episode % 10 == 0 or episode <= 10:
                        additional_metrics = {}
                        if hasattr(self.agent, 'get_safety_metrics'):
                            additional_metrics.update(self.agent.get_safety_metrics())
                        if hasattr(self.agent, 'get_meta_metrics'):
                            additional_metrics.update(self.agent.get_meta_metrics())
                        
                        self.logger.log_training_progress(
                            episode=episode,
                            reward=episode_metrics["total_reward"],
                            safety_violations=episode_metrics["safety_violations"],
                            episode_length=episode_metrics["episode_length"],
                            additional_metrics=additional_metrics
                        )
                    
                    # Store results
                    self.training_results["timesteps"].append(timestep)
                    self.training_results["episodes"].append(episode)
                    self.training_results["rewards"].append(episode_metrics["total_reward"])
                    self.training_results["episode_lengths"].append(episode_metrics["episode_length"])
                    self.training_results["safety_violations"].append(episode_metrics["safety_violations"])
                    self.training_results["constraint_violations"].append(episode_metrics["constraint_violations"])
                    
                    # Reset for next episode
                    obs, info = self.env.reset()
                    self.agent.reset_episode()
                
                # Agent update
                if timestep % self.config.get("update_freq", 2048) == 0:
                    training_metrics = self.agent.update()
                    if training_metrics:
                        self.training_results["training_metrics"].append({
                            "timestep": timestep,
                            "episode": episode,
                            **training_metrics
                        })
                        
                        # Log training metrics
                        if timestep % self.log_freq == 0:
                            self.logger.log_metrics(training_metrics, timestep)
                
                # Evaluation
                if timestep % self.eval_freq == 0 and timestep > 0:
                    eval_results = self.evaluate()
                    self.training_results["evaluation_results"].append({
                        "timestep": timestep,
                        "episode": episode,
                        **eval_results
                    })
                    
                    self.logger.log_evaluation_results(eval_results)
                    
                    # Check for improvement
                    current_reward = eval_results["mean_reward"]
                    current_safety_score = eval_results.get("safety_score", 0)
                    
                    improved = False
                    if current_reward > self.best_reward + self.min_improvement:
                        self.best_reward = current_reward
                        improved = True
                    
                    if current_safety_score > self.best_safety_score + self.min_improvement:
                        self.best_safety_score = current_safety_score
                        improved = True
                    
                    if improved:
                        self.episodes_without_improvement = 0
                        # Save best model
                        if self.config.get("save_model", True):
                            best_model_path = self.model_dir / "best_model.pt"
                            self.agent.save(str(best_model_path))
                            self.logger.log_model_save(str(best_model_path), eval_results)
                    else:
                        self.episodes_without_improvement += 1
                    
                    # Early stopping check
                    if (self.early_stopping and 
                        self.episodes_without_improvement >= self.patience):
                        self.logger.info(f"Early stopping after {self.episodes_without_improvement} evaluations without improvement")
                        break
                
                # Save checkpoint
                if timestep % self.save_freq == 0 and timestep > 0:
                    checkpoint_path = self.model_dir / f"checkpoint_{timestep}.pt"
                    self.agent.save(str(checkpoint_path))
                
                # Update progress
                progress.update(task, completed=timestep)
        
        # Final evaluation
        final_eval = self.evaluate()
        
        # Training summary
        training_time = time.time() - start_time
        final_results = {
            "total_timesteps": timestep,
            "total_episodes": episode,
            "training_time": training_time,
            "final_reward": final_eval["mean_reward"],
            "best_reward": self.best_reward,
            "total_violations": sum(self.training_results["safety_violations"]),
            "final_evaluation": final_eval,
        }
        
        # Save final model
        if self.config.get("save_model", True):
            final_model_path = self.model_dir / "final_model.pt"
            self.agent.save(str(final_model_path))
        
        # Save training results
        self.save_results()
        
        self.logger.log_experiment_end(final_results)
        
        return final_results
    
    def evaluate(self) -> Dict[str, Any]:
        """Evaluate agent performance"""
        eval_rewards = []
        eval_lengths = []
        eval_safety_violations = []
        eval_constraint_violations = []
        eval_costs = []
        
        for episode in range(self.n_eval_episodes):
            obs, info = self.env.reset()
            episode_reward = 0
            episode_length = 0
            episode_violations = 0
            episode_constraints = 0
            episode_cost = 0
            
            done = False
            while not done:
                action = self.agent.select_action(obs, deterministic=True)
                obs, reward, terminated, truncated, info = self.env.step(action)
                done = terminated or truncated
                
                episode_reward += reward
                episode_length += 1
                
                if info.get("safety_violation", False):
                    episode_violations += 1
                if info.get("constraint_violation", False):
                    episode_constraints += 1
                
                episode_cost += info.get("cost", 0)
            
            eval_rewards.append(episode_reward)
            eval_lengths.append(episode_length)
            eval_safety_violations.append(episode_violations)
            eval_constraint_violations.append(episode_constraints)
            eval_costs.append(episode_cost)
        
        # Compute evaluation metrics
        eval_results = {
            "mean_reward": np.mean(eval_rewards),
            "std_reward": np.std(eval_rewards),
            "min_reward": np.min(eval_rewards),
            "max_reward": np.max(eval_rewards),
            "mean_length": np.mean(eval_lengths),
            "mean_safety_violations": np.mean(eval_safety_violations),
            "mean_constraint_violations": np.mean(eval_constraint_violations),
            "mean_cost": np.mean(eval_costs),
            "violation_rate": np.mean([v > 0 for v in eval_safety_violations]),
            "constraint_rate": np.mean([c > 0 for c in eval_constraint_violations]),
        }
        
        # Compute safety score (reward penalized by violations)
        safety_penalty = eval_results["mean_safety_violations"] * 10 + eval_results["mean_constraint_violations"] * 2
        eval_results["safety_score"] = eval_results["mean_reward"] - safety_penalty
        
        return eval_results
    
    def save_results(self):
        """Save training results"""
        import pickle
        import json
        
        # Save as pickle for full data
        with open(self.experiment_dir / "training_results.pkl", 'wb') as f:
            pickle.dump(self.training_results, f)
        
        # Save summary as JSON
        summary = {
            "total_episodes": len(self.training_results["episodes"]),
            "total_timesteps": self.training_results["timesteps"][-1] if self.training_results["timesteps"] else 0,
            "final_reward": self.training_results["rewards"][-1] if self.training_results["rewards"] else 0,
            "mean_reward": np.mean(self.training_results["rewards"]) if self.training_results["rewards"] else 0,
            "total_safety_violations": sum(self.training_results["safety_violations"]),
            "total_constraint_violations": sum(self.training_results["constraint_violations"]),
            "config": self.config,
        }
        
        with open(self.experiment_dir / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        self.logger.info(f"Results saved to {self.experiment_dir}")
    
    def save_model(self) -> str:
        """Save final model and return path"""
        model_path = self.model_dir / "final_model.pt"
        self.agent.save(str(model_path))
        return str(model_path)
