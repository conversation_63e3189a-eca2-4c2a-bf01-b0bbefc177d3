"""
Environment Factory for creating safe RL environments
"""

from typing import Dict, Any, Optional
import gymnasium as gym
import logging

from .cartpole_safe import SafeCartPoleEnv
from .lunar_safe import SafeLunarLanderEnv
from .healthcare_sim import HealthcareSimEnv
from .safety_gym_wrapper import SafetyGymWrapper

logger = logging.getLogger(__name__)

class EnvironmentFactory:
    """Factory for creating safe RL environments"""
    
    def __init__(self):
        self.env_registry = {
            "cartpole_safe": SafeCartPoleEnv,
            "lunar_safe": SafeLunarLanderEnv,
            "healthcare_sim": HealthcareSimEnv,
            "safety_gym": SafetyGymWrapper,
        }
    
    def create_environment(self, env_name: str, config: Dict[str, Any]):
        """Create environment of specified type"""
        
        if env_name not in self.env_registry:
            raise ValueError(f"Unknown environment: {env_name}. "
                           f"Available environments: {list(self.env_registry.keys())}")
        
        # Get environment config
        env_config = config.get("environment", {})
        
        # Create environment
        env_class = self.env_registry[env_name]
        
        try:
            if env_name == "safety_gym":
                # Safety Gym requires special handling
                env = env_class(config=env_config)
            else:
                env = env_class(**env_config)
            
            logger.info(f"Created environment: {env_name}")
            logger.debug(f"Observation space: {env.observation_space}")
            logger.debug(f"Action space: {env.action_space}")
            
            return env
            
        except Exception as e:
            logger.error(f"Failed to create environment {env_name}: {str(e)}")
            raise
    
    def register_environment(self, name: str, env_class: type):
        """Register new environment type"""
        self.env_registry[name] = env_class
        logger.info(f"Registered environment type: {name}")
    
    def list_environments(self) -> list:
        """List available environment types"""
        return list(self.env_registry.keys())
    
    def get_environment_info(self, env_name: str) -> Dict[str, Any]:
        """Get information about environment type"""
        if env_name not in self.env_registry:
            raise ValueError(f"Unknown environment: {env_name}")
        
        env_class = self.env_registry[env_name]
        
        return {
            "name": env_name,
            "class": env_class.__name__,
            "module": env_class.__module__,
            "docstring": env_class.__doc__,
        }
