"""
Advanced Logging System with Rich Console Support
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

# Install rich traceback handler
install(show_locals=True)

class SafeRLLogger:
    """Enhanced logger for Safe RL experiments"""
    
    def __init__(self, name: str, level: int = logging.INFO, console: Optional[Console] = None):
        self.name = name
        self.level = level
        self.console = console or Console()
        self.logger = self._setup_logger()
        self.metrics = {}
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logger with rich handler"""
        logger = logging.getLogger(self.name)
        logger.setLevel(self.level)
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Rich console handler
        rich_handler = RichHandler(
            console=self.console,
            show_time=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True
        )
        rich_handler.setLevel(self.level)
        
        # Formatter
        formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]"
        )
        rich_handler.setFormatter(formatter)
        
        logger.addHandler(rich_handler)
        
        # File handler for persistent logging
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"{self.name}_{timestamp}.log"
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message, **kwargs)
    
    def log_metrics(self, metrics: Dict[str, float], step: int = None):
        """Log metrics with rich formatting"""
        self.metrics.update(metrics)
        
        # Format metrics for display
        metric_str = " | ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        
        if step is not None:
            self.console.print(f"[bold blue]Step {step}[/bold blue] | {metric_str}")
        else:
            self.console.print(f"[bold blue]Metrics[/bold blue] | {metric_str}")
    
    def log_training_progress(self, episode: int, reward: float, safety_violations: int, 
                            episode_length: int, additional_metrics: Dict[str, float] = None):
        """Log training progress with rich formatting"""
        
        # Base metrics
        metrics = {
            "Episode": episode,
            "Reward": reward,
            "Safety Violations": safety_violations,
            "Episode Length": episode_length
        }
        
        if additional_metrics:
            metrics.update(additional_metrics)
        
        # Color code based on safety violations
        if safety_violations == 0:
            color = "green"
        elif safety_violations <= 2:
            color = "yellow"
        else:
            color = "red"
        
        self.console.print(
            f"[{color}]Episode {episode:4d}[/{color}] | "
            f"Reward: {reward:8.2f} | "
            f"Violations: {safety_violations:2d} | "
            f"Length: {episode_length:3d}"
        )
        
        # Log additional metrics if provided
        if additional_metrics:
            metric_str = " | ".join([f"{k}: {v:.4f}" for k, v in additional_metrics.items()])
            self.console.print(f"           {metric_str}")
    
    def log_evaluation_results(self, results: Dict[str, Any]):
        """Log evaluation results"""
        self.console.print("\n[bold blue]Evaluation Results[/bold blue]")
        self.console.print("=" * 50)
        
        for key, value in results.items():
            if isinstance(value, float):
                self.console.print(f"{key:20s}: {value:8.4f}")
            else:
                self.console.print(f"{key:20s}: {value}")
        
        self.console.print("=" * 50)
    
    def create_progress_bar(self, total: int, description: str = "Progress"):
        """Create a rich progress bar"""
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        )
    
    def log_experiment_start(self, config: Dict[str, Any]):
        """Log experiment start with configuration"""
        self.console.print("\n[bold green]Starting Safe RL Experiment[/bold green]")
        self.console.print("=" * 60)
        
        # Log key configuration
        if "experiment_name" in config:
            self.console.print(f"Experiment: {config['experiment_name']}")
        if "env_name" in config:
            self.console.print(f"Environment: {config['env_name']}")
        if "agent_type" in config:
            self.console.print(f"Agent: {config['agent_type']}")
        if "device" in config:
            self.console.print(f"Device: {config['device']}")
        if "seed" in config:
            self.console.print(f"Seed: {config['seed']}")
        
        self.console.print("=" * 60)
    
    def log_experiment_end(self, results: Dict[str, Any]):
        """Log experiment completion"""
        self.console.print("\n[bold green]Experiment Completed[/bold green]")
        self.console.print("=" * 60)
        
        # Log final results
        for key, value in results.items():
            if isinstance(value, float):
                self.console.print(f"{key:25s}: {value:10.4f}")
            else:
                self.console.print(f"{key:25s}: {value}")
        
        self.console.print("=" * 60)
    
    def log_safety_violation(self, episode: int, step: int, violation_type: str, 
                           severity: float, context: Dict[str, Any] = None):
        """Log safety violations with detailed context"""
        self.console.print(
            f"[red]SAFETY VIOLATION[/red] | "
            f"Episode {episode} Step {step} | "
            f"Type: {violation_type} | "
            f"Severity: {severity:.4f}"
        )
        
        if context:
            for key, value in context.items():
                self.console.print(f"  {key}: {value}")
    
    def log_model_save(self, path: str, metrics: Dict[str, float] = None):
        """Log model saving"""
        self.console.print(f"[green]Model saved to: {path}[/green]")
        
        if metrics:
            self.console.print("Model performance:")
            for key, value in metrics.items():
                self.console.print(f"  {key}: {value:.4f}")

def setup_logger(name: str, level: int = logging.INFO, 
                console: Optional[Console] = None) -> SafeRLLogger:
    """Setup a SafeRL logger"""
    return SafeRLLogger(name, level, console)

# Global logger instance
_global_logger = None

def get_logger(name: str = "safe_rl") -> SafeRLLogger:
    """Get global logger instance"""
    global _global_logger
    if _global_logger is None:
        _global_logger = setup_logger(name)
    return _global_logger
