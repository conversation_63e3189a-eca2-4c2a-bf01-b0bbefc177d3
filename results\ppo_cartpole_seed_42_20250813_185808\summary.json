{"total_episodes": 2324, "total_timesteps": 49990, "final_reward": 11.5, "mean_reward": 14.34552495697074, "total_safety_violations": 3317, "total_constraint_violations": 92, "config": {"agent": {"adaptation_steps": 5, "batch_size": 64, "clip_range": 0.2, "constraint_threshold": 0.1, "ent_coef": 0.01, "gae_lambda": 0.95, "gamma": 0.99, "lagrange_multiplier": 0.1, "learning_rate": 0.0003, "max_grad_norm": 0.5, "meta_batch_size": 32, "meta_learning_rate": 0.0001, "n_epochs": 10, "safety_coef": 1.0, "vf_coef": 0.5}, "environment": {"constraint_type": "hard", "env_params": {"pole_angle_limit": 0.2}, "max_episode_steps": 500, "reward_scale": 1.0, "safety_budget": 0.1}, "training": {"total_timesteps": 100000, "eval_freq": 5000, "n_eval_episodes": 10, "save_freq": 10000, "log_freq": 1000, "save_model": true, "save_replay_buffer": false, "early_stopping": false, "patience": 10, "min_improvement": 0.01}, "eval_freq": 5000, "n_eval_episodes": 10, "save_model": true, "early_stopping": false, "experiment_name": "ppo_cartpole_seed_42", "seed": 42, "total_timesteps": 50000, "device": "cuda:0"}}