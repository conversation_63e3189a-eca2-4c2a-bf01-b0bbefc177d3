#!/usr/bin/env python3
"""
Installation Verification Script for Safe RL Framework
"""

import sys
import importlib
import platform
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def check_python_version() -> Tuple[bool, str]:
    """Check Python version compatibility"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        return True, f"Python {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"Python {version.major}.{version.minor}.{version.micro} (requires 3.8+)"

def check_package(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """Check if a package is installed and importable"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        return True, version
    except ImportError as e:
        return False, str(e)

def check_pytorch_gpu() -> Tuple[bool, str]:
    """Check PyTorch GPU availability"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return True, f"{gpu_count} GPU(s) available - {gpu_name}"
        else:
            return False, "CUDA not available"
    except ImportError:
        return False, "PyTorch not installed"

def check_project_structure() -> Tuple[bool, List[str]]:
    """Check project directory structure"""
    required_dirs = [
        "agents",
        "environments", 
        "experiments",
        "utils",
        "configs",
        "scripts"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        if not (project_root / dir_name).exists():
            missing_dirs.append(dir_name)
    
    return len(missing_dirs) == 0, missing_dirs

def check_project_imports() -> Dict[str, Tuple[bool, str]]:
    """Check if project modules can be imported"""
    modules_to_check = {
        "utils.device_manager": "DeviceManager",
        "utils.config_manager": "ConfigManager", 
        "utils.logger": "SafeRLLogger",
        "agents.agent_factory": "AgentFactory",
        "environments.env_factory": "EnvironmentFactory",
        "experiments.training_runner": "TrainingRunner",
    }
    
    results = {}
    for module_name, class_name in modules_to_check.items():
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_name):
                results[module_name] = (True, f"✓ {class_name} available")
            else:
                results[module_name] = (False, f"✗ {class_name} not found")
        except ImportError as e:
            results[module_name] = (False, f"✗ Import error: {str(e)}")
    
    return results

def test_environment_creation() -> Dict[str, Tuple[bool, str]]:
    """Test environment creation"""
    results = {}
    
    try:
        from environments.env_factory import EnvironmentFactory
        factory = EnvironmentFactory()
        
        # Test each environment
        env_configs = {
            "cartpole_safe": {"max_episode_steps": 10},
            "lunar_safe": {"max_episode_steps": 10},
            "healthcare_sim": {"max_episode_steps": 10},
            "safety_gym": {"max_episode_steps": 10}
        }
        
        for env_name, config in env_configs.items():
            try:
                env = factory.create_environment(env_name, {"environment": config})
                obs, info = env.reset()
                action = env.action_space.sample()
                obs, reward, terminated, truncated, info = env.step(action)
                env.close()
                results[env_name] = (True, "✓ Environment working")
            except Exception as e:
                results[env_name] = (False, f"✗ Error: {str(e)}")
    
    except ImportError as e:
        results["environment_factory"] = (False, f"✗ Cannot import EnvironmentFactory: {str(e)}")
    
    return results

def test_agent_creation() -> Dict[str, Tuple[bool, str]]:
    """Test agent creation"""
    results = {}
    
    try:
        from agents.agent_factory import AgentFactory
        from environments.cartpole_safe import SafeCartPoleEnv
        import torch
        
        # Create test environment
        env = SafeCartPoleEnv(max_episode_steps=10)
        factory = AgentFactory()
        
        # Test each agent type
        agent_types = ["ppo", "meta_ppo", "safe_ppo", "transformer_ppo"]
        
        for agent_type in agent_types:
            try:
                config = {
                    "agent": {"learning_rate": 3e-4},
                    "device": torch.device("cpu")
                }
                agent = factory.create_agent(agent_type, env, config)
                
                # Test basic functionality
                obs, _ = env.reset()
                action = agent.select_action(obs, deterministic=True)
                
                results[agent_type] = (True, "✓ Agent working")
            except Exception as e:
                results[agent_type] = (False, f"✗ Error: {str(e)}")
        
        env.close()
    
    except ImportError as e:
        results["agent_factory"] = (False, f"✗ Cannot import AgentFactory: {str(e)}")
    
    return results

def main():
    """Main verification function"""
    console.print(Panel.fit("🔍 Safe RL Framework Installation Verification", style="bold blue"))
    
    # System Information
    console.print("\n[bold]System Information[/bold]")
    system_table = Table(show_header=True, header_style="bold magenta")
    system_table.add_column("Component", style="cyan")
    system_table.add_column("Status", style="green")
    
    # Python version
    python_ok, python_info = check_python_version()
    system_table.add_row("Python Version", f"{'✓' if python_ok else '✗'} {python_info}")
    
    # Platform
    system_table.add_row("Platform", f"✓ {platform.system()} {platform.release()}")
    
    # PyTorch GPU
    gpu_ok, gpu_info = check_pytorch_gpu()
    system_table.add_row("PyTorch GPU", f"{'✓' if gpu_ok else '✗'} {gpu_info}")
    
    console.print(system_table)
    
    # Package Dependencies
    console.print("\n[bold]Package Dependencies[/bold]")
    
    required_packages = [
        ("torch", "torch"),
        ("numpy", "numpy"),
        ("gymnasium", "gymnasium"),
        ("matplotlib", "matplotlib"),
        ("rich", "rich"),
        ("pyyaml", "yaml"),
        ("scipy", "scipy"),
        ("pandas", "pandas"),
        ("tqdm", "tqdm"),
    ]
    
    optional_packages = [
        ("safety-gym", "safety_gym"),
        ("mujoco", "mujoco"),
        ("pygame", "pygame"),
        ("opencv-python", "cv2"),
    ]
    
    with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
        task = progress.add_task("Checking packages...", total=len(required_packages) + len(optional_packages))
        
        package_table = Table(show_header=True, header_style="bold magenta")
        package_table.add_column("Package", style="cyan")
        package_table.add_column("Status", style="green")
        package_table.add_column("Version")
        
        # Required packages
        for package_name, import_name in required_packages:
            ok, info = check_package(package_name, import_name)
            status = "✓ Required" if ok else "✗ Missing"
            package_table.add_row(package_name, status, info)
            progress.advance(task)
        
        # Optional packages
        for package_name, import_name in optional_packages:
            ok, info = check_package(package_name, import_name)
            status = "✓ Optional" if ok else "- Optional"
            package_table.add_row(package_name, status, info)
            progress.advance(task)
    
    console.print(package_table)
    
    # Project Structure
    console.print("\n[bold]Project Structure[/bold]")
    structure_ok, missing_dirs = check_project_structure()
    
    if structure_ok:
        console.print("✓ All required directories present")
    else:
        console.print(f"✗ Missing directories: {', '.join(missing_dirs)}")
    
    # Project Imports
    console.print("\n[bold]Project Module Imports[/bold]")
    import_results = check_project_imports()
    
    import_table = Table(show_header=True, header_style="bold magenta")
    import_table.add_column("Module", style="cyan")
    import_table.add_column("Status")
    
    for module, (ok, info) in import_results.items():
        import_table.add_row(module, info)
    
    console.print(import_table)
    
    # Environment Tests
    console.print("\n[bold]Environment Tests[/bold]")
    with console.status("[bold green]Testing environments..."):
        env_results = test_environment_creation()
    
    env_table = Table(show_header=True, header_style="bold magenta")
    env_table.add_column("Environment", style="cyan")
    env_table.add_column("Status")
    
    for env_name, (ok, info) in env_results.items():
        env_table.add_row(env_name, info)
    
    console.print(env_table)
    
    # Agent Tests
    console.print("\n[bold]Agent Tests[/bold]")
    with console.status("[bold green]Testing agents..."):
        agent_results = test_agent_creation()
    
    agent_table = Table(show_header=True, header_style="bold magenta")
    agent_table.add_column("Agent", style="cyan")
    agent_table.add_column("Status")
    
    for agent_name, (ok, info) in agent_results.items():
        agent_table.add_row(agent_name, info)
    
    console.print(agent_table)
    
    # Summary
    console.print("\n[bold]Verification Summary[/bold]")
    
    all_checks = [
        ("Python Version", python_ok),
        ("PyTorch GPU", gpu_ok),
        ("Project Structure", structure_ok),
    ]
    
    # Add package checks
    for package_name, import_name in required_packages:
        ok, _ = check_package(package_name, import_name)
        all_checks.append((f"Package: {package_name}", ok))
    
    # Add import checks
    for module, (ok, _) in import_results.items():
        all_checks.append((f"Import: {module}", ok))
    
    # Add environment checks
    for env_name, (ok, _) in env_results.items():
        all_checks.append((f"Environment: {env_name}", ok))
    
    # Add agent checks
    for agent_name, (ok, _) in agent_results.items():
        all_checks.append((f"Agent: {agent_name}", ok))
    
    passed = sum(1 for _, ok in all_checks if ok)
    total = len(all_checks)
    
    if passed == total:
        console.print(f"[bold green]✓ All {total} checks passed! Installation is ready.[/bold green]")
        console.print("\n[bold]Next steps:[/bold]")
        console.print("1. Run: [cyan]python main.py --env cartpole_safe --agent ppo[/cyan]")
        console.print("2. Or run experiments: [cyan]python experiments/main_experiments.py[/cyan]")
    else:
        console.print(f"[bold yellow]⚠ {passed}/{total} checks passed. Some issues found.[/bold yellow]")
        console.print("\n[bold]To fix issues:[/bold]")
        console.print("1. Install missing packages: [cyan]pip install -r requirements.txt[/cyan]")
        console.print("2. Check error messages above for specific issues")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
