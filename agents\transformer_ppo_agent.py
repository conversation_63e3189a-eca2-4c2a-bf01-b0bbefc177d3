"""
Transformer-based PPO Agent with Attention Mechanisms
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, Any, Tuple, List, Optional
from gymnasium import spaces
import math
import logging

from .ppo_agent import PPOAgent
from .base_agent import NetworkUtils

logger = logging.getLogger(__name__)

class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism"""
    
    def __init__(self, d_model: int, n_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size, seq_len = query.size(0), query.size(1)
        
        # Linear transformations
        Q = self.w_q(query).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        output = self.w_o(context)
        
        return output, attention_weights

class TransformerBlock(nn.Module):
    """Transformer encoder block"""
    
    def __init__(self, d_model: int, n_heads: int = 8, d_ff: int = 2048, dropout: float = 0.1):
        super().__init__()
        
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        # Self-attention
        attn_output, attention_weights = self.attention(x, x, x, mask)
        x = self.norm1(x + attn_output)
        
        # Feed-forward
        ff_output = self.feed_forward(x)
        x = self.norm2(x + ff_output)
        
        return x, attention_weights

class TransformerEncoder(nn.Module):
    """Transformer encoder for sequence processing"""
    
    def __init__(self, d_model: int, n_layers: int = 6, n_heads: int = 8, 
                 d_ff: int = 2048, dropout: float = 0.1, max_seq_len: int = 1000):
        super().__init__()
        
        self.d_model = d_model
        self.max_seq_len = max_seq_len
        
        # Positional encoding
        self.pos_encoding = self._create_positional_encoding(max_seq_len, d_model)
        self.dropout = nn.Dropout(dropout)
        
        # Transformer blocks
        self.layers = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        self.norm = nn.LayerNorm(d_model)
    
    def _create_positional_encoding(self, max_len: int, d_model: int) -> torch.Tensor:
        """Create positional encoding"""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe.unsqueeze(0)
    
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        batch_size, seq_len = x.size(0), x.size(1)
        
        # Add positional encoding
        pos_enc = self.pos_encoding[:, :seq_len, :].to(x.device)
        x = x + pos_enc
        x = self.dropout(x)
        
        # Apply transformer layers
        attention_weights = []
        for layer in self.layers:
            x, attn_weights = layer(x, mask)
            attention_weights.append(attn_weights)
        
        x = self.norm(x)
        
        return x, attention_weights

class TransformerActorCritic(nn.Module):
    """Transformer-based Actor-Critic network"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space,
                 d_model: int = 256, n_layers: int = 4, n_heads: int = 8,
                 sequence_length: int = 50, dropout: float = 0.1):
        super().__init__()
        
        self.observation_space = observation_space
        self.action_space = action_space
        self.d_model = d_model
        self.sequence_length = sequence_length
        
        # Get dimensions
        obs_shape = NetworkUtils.get_obs_shape(observation_space)
        if len(obs_shape) == 1:
            obs_dim = obs_shape[0]
        else:
            obs_dim = np.prod(obs_shape)
        
        # Input embedding
        self.input_embedding = nn.Linear(obs_dim, d_model)
        
        # Transformer encoder
        self.transformer = TransformerEncoder(
            d_model=d_model,
            n_layers=n_layers,
            n_heads=n_heads,
            d_ff=d_model * 4,
            dropout=dropout,
            max_seq_len=sequence_length
        )
        
        # Actor head
        if isinstance(action_space, spaces.Box):
            # Continuous actions
            action_dim = action_space.shape[0]
            self.actor_mean = nn.Linear(d_model, action_dim)
            self.actor_logstd = nn.Parameter(torch.zeros(action_dim))
            self.continuous = True
        elif isinstance(action_space, spaces.Discrete):
            # Discrete actions
            action_dim = action_space.n
            self.actor_head = nn.Linear(d_model, action_dim)
            self.continuous = False
        else:
            raise NotImplementedError(f"Action space {type(action_space)} not supported")
        
        # Critic head
        self.critic_head = nn.Linear(d_model, 1)
        
        # Safety critic for constraint handling
        self.safety_critic = nn.Linear(d_model, 1)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize weights"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, obs_sequence: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, List[torch.Tensor]]:
        """Forward pass through transformer"""
        batch_size, seq_len = obs_sequence.size(0), obs_sequence.size(1)
        
        # Flatten observations if needed
        if obs_sequence.dim() > 3:
            obs_sequence = obs_sequence.view(batch_size, seq_len, -1)
        
        # Input embedding
        embedded = self.input_embedding(obs_sequence)
        
        # Transformer encoding
        encoded, attention_weights = self.transformer(embedded, mask)
        
        # Use the last timestep for action/value prediction
        last_hidden = encoded[:, -1, :]  # [batch_size, d_model]
        
        # Actor
        if self.continuous:
            action_mean = self.actor_mean(last_hidden)
            action_std = torch.exp(self.actor_logstd)
            action_dist = Normal(action_mean, action_std)
        else:
            action_logits = self.actor_head(last_hidden)
            action_dist = Categorical(logits=action_logits)
        
        # Critics
        value = self.critic_head(last_hidden).squeeze(-1)
        safety_value = self.safety_critic(last_hidden).squeeze(-1)
        
        return action_dist, value, safety_value, attention_weights

class TransformerPPOAgent(PPOAgent):
    """Transformer-based PPO Agent with Attention Mechanisms"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 config: Dict[str, Any], device: torch.device):
        # Initialize sequence buffer before calling parent
        self.sequence_length = config.get("sequence_length", 50)
        self.observation_buffer = []
        
        super().__init__(observation_space, action_space, config, device)
        
        # Transformer-specific parameters
        self.d_model = config.get("d_model", 256)
        self.n_layers = config.get("n_layers", 4)
        self.n_heads = config.get("n_heads", 8)
        self.dropout = config.get("dropout", 0.1)
        
        # Attention analysis
        self.attention_weights_history = []
        self.max_attention_history = config.get("max_attention_history", 100)
    
    def _build_networks(self):
        """Build transformer-based networks"""
        self.actor_critic = TransformerActorCritic(
            self.observation_space,
            self.action_space,
            d_model=self.d_model,
            n_layers=self.n_layers,
            n_heads=self.n_heads,
            sequence_length=self.sequence_length,
            dropout=self.dropout
        ).to(self.device)
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.actor_critic.parameters(),
            lr=self.learning_rate,
            eps=1e-5,
            weight_decay=1e-4  # Add weight decay for transformer
        )
        
        logger.info(f"Built Transformer PPO networks with {sum(p.numel() for p in self.actor_critic.parameters())} parameters")
    
    def _get_observation_sequence(self, current_obs: np.ndarray) -> torch.Tensor:
        """Get observation sequence for transformer input"""
        # Add current observation to buffer
        self.observation_buffer.append(current_obs)
        
        # Keep only recent observations
        if len(self.observation_buffer) > self.sequence_length:
            self.observation_buffer = self.observation_buffer[-self.sequence_length:]
        
        # Pad sequence if needed
        sequence = self.observation_buffer.copy()
        while len(sequence) < self.sequence_length:
            sequence.insert(0, np.zeros_like(current_obs))
        
        # Convert to tensor
        sequence_tensor = torch.FloatTensor(np.array(sequence)).unsqueeze(0).to(self.device)
        
        return sequence_tensor
    
    def select_action(self, observation: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """Select action using transformer with attention"""
        with torch.no_grad():
            # Get observation sequence
            obs_sequence = self._get_observation_sequence(observation)
            
            # Create attention mask (optional - can mask padded positions)
            mask = None
            
            # Forward pass
            action_dist, value, safety_value, attention_weights = self.actor_critic(obs_sequence, mask)
            
            if deterministic:
                if self.actor_critic.continuous:
                    action = action_dist.mean
                else:
                    action = action_dist.probs.argmax(dim=-1)
            else:
                action = action_dist.sample()
            
            log_prob = action_dist.log_prob(action)
            
            # Store attention weights for analysis
            if len(self.attention_weights_history) < self.max_attention_history:
                self.attention_weights_history.append([aw.cpu().numpy() for aw in attention_weights])
            
            # Store for training
            if not deterministic:
                self.buffer["observations"].append(observation)
                self.buffer["actions"].append(action.cpu().numpy())
                self.buffer["values"].append(value.cpu().numpy())
                self.buffer["log_probs"].append(log_prob.cpu().numpy())
                
                # Store additional transformer-specific data
                if "safety_values" not in self.buffer:
                    self.buffer["safety_values"] = []
                self.buffer["safety_values"].append(safety_value.cpu().numpy())
            
            return action.cpu().numpy().squeeze()
    
    def get_attention_analysis(self) -> Dict[str, Any]:
        """Get attention analysis for interpretability"""
        if not self.attention_weights_history:
            return {}
        
        # Analyze attention patterns
        analysis = {
            "num_samples": len(self.attention_weights_history),
            "num_layers": len(self.attention_weights_history[0]),
            "num_heads": self.attention_weights_history[0][0].shape[1],
            "sequence_length": self.attention_weights_history[0][0].shape[-1],
        }
        
        # Compute average attention weights across samples
        avg_attention_per_layer = []
        for layer_idx in range(analysis["num_layers"]):
            layer_attentions = [sample[layer_idx] for sample in self.attention_weights_history]
            avg_attention = np.mean(layer_attentions, axis=0)  # Average across samples
            avg_attention_per_layer.append(avg_attention)
        
        analysis["average_attention_per_layer"] = avg_attention_per_layer
        
        # Compute attention entropy (measure of attention spread)
        attention_entropies = []
        for layer_idx in range(analysis["num_layers"]):
            layer_attentions = avg_attention_per_layer[layer_idx]
            # Compute entropy for each head
            head_entropies = []
            for head_idx in range(analysis["num_heads"]):
                head_attention = layer_attentions[0, head_idx, -1, :]  # Last position attention
                entropy = -np.sum(head_attention * np.log(head_attention + 1e-8))
                head_entropies.append(entropy)
            attention_entropies.append(head_entropies)
        
        analysis["attention_entropies"] = attention_entropies
        
        return analysis
    
    def reset_episode(self):
        """Reset episode and clear observation buffer"""
        super().reset_episode()
        self.observation_buffer = []
    
    def update(self, batch: Dict[str, Any] = None) -> Dict[str, float]:
        """Update transformer with sequence-aware training"""
        if len(self.buffer["observations"]) < self.batch_size:
            return self.training_metrics
        
        # For transformer training, we need to handle sequences properly
        # This is a simplified version - full implementation would batch sequences
        
        # Standard PPO update (simplified for now)
        training_metrics = super().update(batch)
        
        # Add transformer-specific metrics
        attention_analysis = self.get_attention_analysis()
        if attention_analysis:
            training_metrics.update({
                "avg_attention_entropy": np.mean(attention_analysis.get("attention_entropies", [0])),
                "attention_samples": attention_analysis.get("num_samples", 0),
            })
        
        return training_metrics
    
    def save(self, path: str):
        """Save transformer agent state"""
        torch.save({
            "actor_critic_state_dict": self.actor_critic.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "training_step": self.training_step,
            "config": self.config,
            "observation_buffer": self.observation_buffer,
            "attention_weights_history": self.attention_weights_history,
        }, path)
        logger.info(f"Saved Transformer PPO agent to {path}")
    
    def load(self, path: str):
        """Load transformer agent state"""
        checkpoint = torch.load(path, map_location=self.device)
        self.actor_critic.load_state_dict(checkpoint["actor_critic_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.training_step = checkpoint["training_step"]
        self.observation_buffer = checkpoint.get("observation_buffer", [])
        self.attention_weights_history = checkpoint.get("attention_weights_history", [])
        logger.info(f"Loaded Transformer PPO agent from {path}")
