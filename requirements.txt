# Core ML/RL Dependencies
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0

# Reinforcement Learning
gymnasium>=0.29.0
stable-baselines3>=2.0.0
sb3-contrib>=2.0.0
tensorboard>=2.10.0
wandb>=0.15.0

# Environment Dependencies
box2d-py>=2.3.5
pygame>=2.1.0
opencv-python>=4.5.0
mujoco>=2.3.0

# Safety Gym (optional - install separately)
# safety-gym

# Data Science & Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
pandas>=1.3.0
jupyter>=1.0.0
ipywidgets>=7.6.0

# Statistical Analysis
statsmodels>=0.13.0
pingouin>=0.5.0
bayesian-optimization>=1.4.0

# Development Tools
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
isort>=5.10.0
mypy>=0.991
flake8>=5.0.0
pre-commit>=2.20.0

# Configuration & Utilities
pyyaml>=6.0
hydra-core>=1.2.0
omegaconf>=2.2.0
tqdm>=4.64.0
rich>=12.0.0
typer>=0.7.0

# Windows-specific optimizations
psutil>=5.9.0
colorama>=0.4.5

# Optional GPU monitoring
gpustat>=1.0.0
nvidia-ml-py3>=7.352.0

# Formal verification (optional)
z3-solver>=4.11.0
sympy>=1.11.0

# Export/Import
pickle5>=0.0.11
joblib>=1.2.0
h5py>=3.7.0
