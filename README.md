# Safe Reinforcement Learning with Meta-Learning Framework

A comprehensive research framework for Safe RL with meta-learning, GPU acceleration, and multi-environment evaluation designed for top-tier research publications.

## 🚀 Features

- **Meta-Learning Safe RL**: Adaptive safety constraints with transformer-based meta-learner
- **GPU Acceleration**: CUDA support with automatic CPU fallback for Windows
- **Multi-Environment**: CartPole, LunarLander, Safety Gym, Healthcare simulation
- **Research-Ready**: Statistical analysis, ablations, interpretability tools
- **Windows Native**: Optimized for Windows development with PyTorch
- **Publication Quality**: Designed for NeurIPS/ICLR/ICML submissions

## 📦 Installation

### Quick Installation
```bash
# Clone repository
git clone https://github.com/your-repo/safe-rl-meta-learning.git
cd safe-rl-meta-learning

# Install with pip
pip install -e .

# Or install dependencies manually
pip install -r requirements.txt

# Verify installation
python scripts/verify_installation.py
```

### Optional Dependencies
```bash
# For Safety Gym environments
pip install safety-gym mujoco

# For advanced visualization
pip install plotly seaborn jupyter

# For development
pip install -e .[dev]
```

## 🎯 Quick Start

### Interactive Setup
```bash
# Run interactive setup
python quick_start.py --setup

# Or run quick demo
python quick_start.py --demo
```

### Basic Training
```bash
# Train PPO agent on Safe CartPole
python main.py --env cartpole_safe --agent ppo --gpu

# Train Safe PPO with constraints
python main.py --env cartpole_safe --agent safe_ppo --total_timesteps 100000

# Train Meta-Learning agent
python main.py --env healthcare_sim --agent meta_ppo --meta_learning

# Train Transformer agent
python main.py --env lunar_safe --agent transformer_ppo --gpu
```

### Experiment Suite
```bash
# Run complete experiment suite
python experiments/main_experiments.py

# Run specific experiment
python experiments/main_experiments.py --single ppo_cartpole

# List available experiments
python experiments/main_experiments.py --list
```

### Analysis and Visualization
```bash
# Generate analysis report
python analysis/generate_report.py results/experiment_name_timestamp/

# View results
# Figures saved to results/experiment_name_timestamp/figures/
```

## 📊 Project Structure

```
safe-rl-meta-learning/
├── agents/                 # RL agents and meta-learners
│   ├── base_agent.py      # Base agent classes
│   ├── ppo_agent.py       # Standard PPO implementation
│   ├── safe_ppo_agent.py  # Safe PPO with constraints
│   ├── meta_ppo_agent.py  # Meta-learning PPO
│   ├── transformer_ppo_agent.py  # Transformer-based PPO
│   └── agent_factory.py   # Agent creation factory
├── environments/          # Safe RL environments
│   ├── cartpole_safe.py   # Safe CartPole environment
│   ├── lunar_safe.py      # Safe LunarLander environment
│   ├── healthcare_sim.py  # Healthcare simulation
│   ├── safety_gym_wrapper.py  # Safety Gym integration
│   └── env_factory.py     # Environment creation factory
├── experiments/           # Experiment runners
│   ├── training_runner.py # Training loop management
│   └── main_experiments.py # Experiment suite runner
├── analysis/             # Analysis and visualization
│   └── generate_report.py # Comprehensive analysis generator
├── utils/                # Utilities and helpers
│   ├── device_manager.py  # GPU/CPU device management
│   ├── config_manager.py  # Configuration management
│   └── logger.py          # Advanced logging system
├── configs/              # Configuration files
├── scripts/              # Utility scripts
│   └── verify_installation.py  # Installation verification
├── results/              # Experiment results (auto-generated)
├── figures/              # Generated plots (auto-generated)
├── metrics/              # Evaluation metrics
├── formal/               # Theoretical analysis
├── main.py               # Main training entry point
├── quick_start.py        # Interactive quick start
├── requirements.txt      # Python dependencies
├── setup.py              # Package setup
└── pyproject.toml        # Modern Python packaging
```

## 🔬 Research Components

### Agents
- **PPO**: Standard Proximal Policy Optimization
- **Safe PPO**: PPO with Lagrangian constraint optimization
- **Meta-PPO**: Meta-learning PPO with adaptive safety constraints
- **Transformer PPO**: Attention-based PPO with sequence modeling

### Environments
- **Safe CartPole**: CartPole with angle and position constraints
- **Safe LunarLander**: LunarLander with fuel and crash constraints
- **Healthcare Simulation**: Drug dosage optimization with safety limits
- **Safety Gym**: Navigation tasks with hazards (optional)

### Key Features for Research
- **Multi-Domain Evaluation**: Test across diverse safety-critical domains
- **Statistical Rigor**: Multiple seeds, significance tests, confidence intervals
- **Interpretability**: Attention visualization, constraint analysis
- **Formal Guarantees**: Safety constraint satisfaction proofs
- **Computational Efficiency**: GPU acceleration, optimized implementations

## 📈 Research Workflow

### 1. Run Experiments
```bash
# Run comprehensive experiment suite
python experiments/main_experiments.py

# Results saved to results/experiment_suite_TIMESTAMP/
```

### 2. Generate Analysis
```bash
# Generate publication-quality figures
python analysis/generate_report.py results/experiment_suite_TIMESTAMP/

# Figures saved as both PNG and PDF for LaTeX
```

### 3. Key Metrics Generated
- **Performance vs Safety Trade-offs**: Pareto frontier analysis
- **Generalization**: Cross-environment transfer learning
- **Sample Efficiency**: Learning curves with confidence intervals
- **Constraint Satisfaction**: Safety budget usage over time
- **Interpretability**: Attention weights, policy analysis

### 4. Publication-Ready Outputs
- High-resolution figures (300 DPI PNG + PDF)
- Statistical significance tests
- Comprehensive experimental logs
- Reproducible configuration files

## 🛠️ Development

### Code Quality
```bash
# Format code
black .
isort .

# Type checking
mypy .

# Linting
flake8 .

# Run tests
python -m pytest tests/
```

### Adding New Components

#### New Agent
```python
# Create new agent in agents/my_agent.py
from agents.base_agent import BaseAgent

class MyAgent(BaseAgent):
    def __init__(self, observation_space, action_space, config, device):
        super().__init__(observation_space, action_space, config, device)
        # Your implementation

# Register in agents/agent_factory.py
```

#### New Environment
```python
# Create new environment in environments/my_env.py
import gymnasium as gym

class MyEnv(gym.Env):
    def __init__(self, **kwargs):
        # Your implementation

# Register in environments/env_factory.py
```

## 🔧 Configuration

### Agent Configuration
```yaml
# configs/agents/my_agent.yaml
learning_rate: 3e-4
batch_size: 64
n_epochs: 10
gamma: 0.99
safety_coef: 1.0
```

### Environment Configuration
```yaml
# configs/environments/my_env.yaml
max_episode_steps: 1000
safety_budget: 0.1
constraint_type: "hard"
```

### Experiment Configuration
```yaml
# Custom experiment configuration
experiments:
  - name: "my_experiment"
    agent: "meta_ppo"
    environment: "cartpole_safe"
    seeds: [42, 123, 456]
    total_timesteps: 100000
```

## 📊 Results and Analysis

### Automatic Analysis Generation
The framework automatically generates:
- **Training Curves**: Reward, episode length, violations over time
- **Safety Analysis**: Violation rates, constraint satisfaction, safety budgets
- **Performance Metrics**: Policy/value losses, entropy, KL divergence
- **Evaluation Results**: Mean rewards with confidence intervals
- **Comparative Analysis**: Multi-agent performance comparison

### Key Research Insights
- **Safety-Performance Trade-offs**: Quantified Pareto frontiers
- **Meta-Learning Benefits**: Adaptation speed across environments
- **Constraint Satisfaction**: Formal safety guarantee verification
- **Computational Efficiency**: Training time vs. performance analysis

## 🎓 Research Applications

This framework is designed for research in:
- **Safe Reinforcement Learning**: Constraint satisfaction, risk-aware policies
- **Meta-Learning**: Few-shot adaptation, transfer learning
- **Multi-Task Learning**: Cross-domain generalization
- **Interpretable AI**: Attention mechanisms, policy analysis
- **Healthcare AI**: Drug dosage, treatment optimization
- **Robotics**: Safe navigation, manipulation

## 📄 Citation

If you use this framework in your research, please cite:

```bibtex
@software{safe_rl_meta_2024,
  title={Safe Reinforcement Learning with Meta-Learning Framework},
  author={Safe RL Research Team},
  year={2024},
  url={https://github.com/your-repo/safe-rl-meta-learning},
  version={1.0.0}
}
```

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure code passes all checks (black, isort, mypy, flake8)
5. Submit a pull request

## 📞 Support

- **Documentation**: See inline docstrings and type hints
- **Issues**: Report bugs on GitHub Issues
- **Discussions**: Join our research discussions
- **Email**: <EMAIL>

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built for researchers, by researchers. Happy researching! 🚀**
