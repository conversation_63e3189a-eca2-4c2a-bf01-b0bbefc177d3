[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "safe-rl-meta-learning"
version = "1.0.0"
description = "Safe Reinforcement Learning with Meta-Learning Framework"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "Safe RL Research Team", email = "<EMAIL>"},
]
keywords = ["reinforcement-learning", "safe-rl", "meta-learning", "pytorch"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "torch>=2.0.0",
    "numpy>=1.21.0",
    "gymnasium>=0.29.0",
    "matplotlib>=3.5.0",
    "rich>=12.0.0",
    "pyyaml>=6.0",
    "pandas>=1.3.0",
    "scipy>=1.7.0",
    "tqdm>=4.64.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "mypy>=0.991",
    "flake8>=5.0.0",
    "pre-commit>=2.20.0",
]
safety_gym = [
    "safety-gym",
    "mujoco>=2.3.0",
]
visualization = [
    "plotly>=5.0.0",
    "seaborn>=0.11.0",
    "jupyter>=1.0.0",
    "ipywidgets>=7.6.0",
]
all = [
    "safe-rl-meta-learning[dev,safety_gym,visualization]",
]

[project.scripts]
safe-rl-train = "main:main"
safe-rl-experiments = "experiments.main_experiments:main"
safe-rl-analyze = "analysis.generate_report:main"
safe-rl-verify = "scripts.verify_installation:main"

[project.urls]
Homepage = "https://github.com/saferl/safe-rl-meta-learning"
Repository = "https://github.com/saferl/safe-rl-meta-learning"
Documentation = "https://safe-rl-meta-learning.readthedocs.io"
"Bug Tracker" = "https://github.com/saferl/safe-rl-meta-learning/issues"

[tool.setuptools.packages.find]
include = ["agents*", "environments*", "experiments*", "utils*", "analysis*", "metrics*", "formal*", "configs*", "scripts*"]

[tool.black]
line-length = 100
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "gymnasium.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "safety_gym.*",
    "mujoco.*",
    "pygame.*",
    "cv2.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gpu: marks tests that require GPU",
]
