"""
Safe PPO Agent with Constraint Optimization
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Normal, Categorical
from typing import Dict, Any, Tuple, List
from gymnasium import spaces
import logging

from .ppo_agent import PPOAgent, ActorCritic
from .base_agent import SafeAgent, NetworkUtils

logger = logging.getLogger(__name__)

class SafeActorCritic(ActorCritic):
    """Actor-Critic with safety constraint handling"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space,
                 hidden_dims: List[int] = [64, 64], activation: nn.Module = nn.Tanh):
        super().__init__(observation_space, action_space, hidden_dims, activation)
        
        # Additional safety critic for constraint values
        self.safety_critic = nn.Linear(hidden_dims[-1], 1)
        
        # Safety-aware action modification
        if isinstance(action_space, spaces.Box):
            self.safety_modifier = nn.Sequential(
                nn.Linear(hidden_dims[-1], hidden_dims[-1] // 2),
                nn.ReLU(),
                nn.Linear(hidden_dims[-1] // 2, action_space.shape[0]),
                nn.Tanh()  # Bounded modification
            )
        
        # Initialize safety components
        NetworkUtils.init_weights(self.safety_critic, gain=1.0)
        if hasattr(self, 'safety_modifier'):
            self.safety_modifier.apply(lambda m: NetworkUtils.init_weights(m, gain=0.01))
    
    def forward(self, obs: torch.Tensor, apply_safety: bool = True) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass with safety constraint evaluation"""
        # Flatten observation if needed
        if obs.dim() > 2:
            obs = obs.view(obs.size(0), -1)
        
        features = self.feature_extractor(obs)
        
        # Actor
        if self.continuous:
            action_mean = self.actor_mean(features)
            
            # Apply safety modification if enabled
            if apply_safety and hasattr(self, 'safety_modifier'):
                safety_mod = self.safety_modifier(features)
                action_mean = action_mean + 0.1 * safety_mod  # Small safety adjustment
            
            action_std = torch.exp(self.actor_logstd)
            action_dist = Normal(action_mean, action_std)
        else:
            action_logits = self.actor_head(features)
            action_dist = Categorical(logits=action_logits)
        
        # Critics
        value = self.critic_head(features)
        safety_value = self.safety_critic(features)
        
        return action_dist, value.squeeze(-1), safety_value.squeeze(-1)

class SafePPOAgent(SafeAgent):
    """Safe PPO Agent with Lagrangian Constraint Optimization"""
    
    def __init__(self, observation_space: spaces.Space, action_space: spaces.Space, 
                 config: Dict[str, Any], device: torch.device):
        super().__init__(observation_space, action_space, config, device)
        
        # PPO hyperparameters
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.batch_size = config.get("batch_size", 64)
        self.n_epochs = config.get("n_epochs", 10)
        self.gamma = config.get("gamma", 0.99)
        self.gae_lambda = config.get("gae_lambda", 0.95)
        self.clip_range = config.get("clip_range", 0.2)
        self.ent_coef = config.get("ent_coef", 0.01)
        self.vf_coef = config.get("vf_coef", 0.5)
        self.max_grad_norm = config.get("max_grad_norm", 0.5)
        
        # Safety-specific parameters
        self.safety_clip_range = config.get("safety_clip_range", 0.1)
        self.cost_limit = config.get("cost_limit", 0.1)
        self.lagrange_lr = config.get("lagrange_lr", 0.01)
        
        # Buffer for storing experiences
        self.buffer_size = config.get("buffer_size", 2048)
        self.buffer = {
            "observations": [],
            "actions": [],
            "rewards": [],
            "costs": [],
            "values": [],
            "safety_values": [],
            "log_probs": [],
            "dones": [],
            "advantages": [],
            "returns": [],
            "cost_advantages": [],
            "cost_returns": [],
        }
        
        # Training metrics
        self.training_metrics = {
            "policy_loss": 0.0,
            "value_loss": 0.0,
            "safety_loss": 0.0,
            "constraint_loss": 0.0,
            "entropy_loss": 0.0,
            "total_loss": 0.0,
            "kl_divergence": 0.0,
            "clip_fraction": 0.0,
            "cost_violation": 0.0,
        }
    
    def _build_networks(self):
        """Build safe actor-critic network"""
        hidden_dims = self.config.get("hidden_dims", [64, 64])
        activation = getattr(nn, self.config.get("activation", "Tanh"))
        
        self.actor_critic = SafeActorCritic(
            self.observation_space,
            self.action_space,
            hidden_dims,
            activation
        ).to(self.device)
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.actor_critic.parameters(),
            lr=self.learning_rate,
            eps=1e-5
        )
        
        # Lagrange multiplier optimizer
        self.lagrange_optimizer = optim.Adam(
            [self.lagrange_multiplier],
            lr=self.lagrange_lr
        )
        
        logger.info(f"Built Safe PPO networks with {sum(p.numel() for p in self.actor_critic.parameters())} parameters")
    
    def select_action(self, observation: np.ndarray, deterministic: bool = False) -> np.ndarray:
        """Select action with safety considerations"""
        with torch.no_grad():
            obs_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
            action_dist, value, safety_value = self.actor_critic(obs_tensor, apply_safety=True)
            
            if deterministic:
                if self.actor_critic.continuous:
                    action = action_dist.mean
                else:
                    action = action_dist.probs.argmax(dim=-1)
            else:
                action = action_dist.sample()
            
            log_prob = action_dist.log_prob(action)
            
            # Store for training
            if not deterministic:
                self.buffer["observations"].append(observation)
                self.buffer["actions"].append(action.cpu().numpy())
                self.buffer["values"].append(value.cpu().numpy())
                self.buffer["safety_values"].append(safety_value.cpu().numpy())
                self.buffer["log_probs"].append(log_prob.cpu().numpy())
            
            return action.cpu().numpy().squeeze()
    
    def store_transition(self, reward: float, done: bool, info: Dict[str, Any] = None):
        """Store transition with safety cost"""
        self.buffer["rewards"].append(reward)
        self.buffer["dones"].append(done)
        
        # Extract safety cost from info
        if info is not None:
            cost = info.get("cost", 0.0)
            if info.get("safety_violation", False):
                cost += 1.0  # Add violation penalty
            if info.get("constraint_violation", False):
                cost += 0.5  # Add constraint penalty
        else:
            cost = 0.0
        
        self.buffer["costs"].append(cost)
        
        # Update safety metrics
        if cost > 0:
            self.safety_metrics["cumulative_violations"] += cost
        
        # Update episode metrics
        self.step(reward, done, info or {})
    
    def compute_safety_loss(self, batch: Dict[str, Any]) -> torch.Tensor:
        """Compute safety-related loss"""
        # Extract safety-related data from batch
        costs = batch.get("costs", torch.zeros(len(batch["rewards"]), device=self.device))
        safety_values = batch.get("safety_values", torch.zeros(len(batch["rewards"]), device=self.device))
        
        # Safety value loss (predicting costs)
        cost_returns = batch.get("cost_returns", costs)  # Simplified
        safety_loss = F.mse_loss(safety_values, cost_returns)
        
        return safety_loss
    
    def compute_gae(self, rewards: List[float], values: List[float], 
                   dones: List[bool], next_value: float = 0.0) -> Tuple[np.ndarray, np.ndarray]:
        """Compute Generalized Advantage Estimation"""
        advantages = []
        gae = 0
        
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_value_i = values[i + 1]
            
            delta = rewards[i] + self.gamma * next_value_i * next_non_terminal - values[i]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages.insert(0, gae)
        
        advantages = np.array(advantages)
        returns = advantages + np.array(values)
        
        return advantages, returns
    
    def update(self, batch: Dict[str, Any] = None) -> Dict[str, float]:
        """Update agent with safety constraints"""
        if len(self.buffer["observations"]) < self.batch_size:
            return self.training_metrics
        
        # Compute advantages and returns for rewards
        advantages, returns = self.compute_gae(
            self.buffer["rewards"],
            self.buffer["values"],
            self.buffer["dones"]
        )
        
        # Compute advantages and returns for costs
        cost_advantages, cost_returns = self.compute_gae(
            self.buffer["costs"],
            self.buffer["safety_values"],
            self.buffer["dones"]
        )
        
        self.buffer["advantages"] = advantages.tolist()
        self.buffer["returns"] = returns.tolist()
        self.buffer["cost_advantages"] = cost_advantages.tolist()
        self.buffer["cost_returns"] = cost_returns.tolist()
        
        # Convert to tensors
        observations = torch.FloatTensor(np.array(self.buffer["observations"])).to(self.device)
        actions = torch.FloatTensor(np.array(self.buffer["actions"])).to(self.device)
        old_log_probs = torch.FloatTensor(np.array(self.buffer["log_probs"])).to(self.device)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        cost_advantages = torch.FloatTensor(cost_advantages).to(self.device)
        cost_returns = torch.FloatTensor(cost_returns).to(self.device)
        costs = torch.FloatTensor(self.buffer["costs"]).to(self.device)
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        cost_advantages = (cost_advantages - cost_advantages.mean()) / (cost_advantages.std() + 1e-8)
        
        # Safe PPO update
        total_policy_loss = 0
        total_value_loss = 0
        total_safety_loss = 0
        total_constraint_loss = 0
        total_entropy_loss = 0
        total_kl_div = 0
        total_clip_fraction = 0
        
        for epoch in range(self.n_epochs):
            # Mini-batch updates
            indices = torch.randperm(len(observations))
            
            for start in range(0, len(observations), self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]
                
                batch_obs = observations[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                batch_cost_advantages = cost_advantages[batch_indices]
                batch_cost_returns = cost_returns[batch_indices]
                batch_costs = costs[batch_indices]
                
                # Forward pass
                action_dist, values, safety_values = self.actor_critic(batch_obs, apply_safety=True)
                new_log_probs = action_dist.log_prob(batch_actions)
                entropy = action_dist.entropy().mean()
                
                # Policy loss with safety constraints
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                
                # Standard PPO objective
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_range, 1 + self.clip_range) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # Safety constraint loss
                cost_surr1 = ratio * batch_cost_advantages
                cost_surr2 = torch.clamp(ratio, 1 - self.safety_clip_range, 1 + self.safety_clip_range) * batch_cost_advantages
                constraint_loss = torch.max(cost_surr1, cost_surr2).mean()
                
                # Value losses
                value_loss = F.mse_loss(values, batch_returns)
                safety_loss = F.mse_loss(safety_values, batch_cost_returns)
                
                # Lagrangian constraint
                avg_cost = batch_costs.mean()
                lagrange_loss = self.lagrange_multiplier * (avg_cost - self.cost_limit)
                
                # Total loss
                total_loss = (policy_loss + 
                            self.vf_coef * value_loss + 
                            self.safety_coef * safety_loss +
                            constraint_loss +
                            lagrange_loss -
                            self.ent_coef * entropy)
                
                # Backward pass
                self.optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.actor_critic.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # Update Lagrange multiplier
                self.lagrange_optimizer.zero_grad()
                lagrange_objective = -lagrange_loss  # Maximize dual objective
                lagrange_objective.backward()
                self.lagrange_optimizer.step()
                
                # Clamp Lagrange multiplier to be non-negative
                with torch.no_grad():
                    self.lagrange_multiplier.clamp_(min=0.0)
                
                # Metrics
                with torch.no_grad():
                    kl_div = (batch_old_log_probs - new_log_probs).mean()
                    clip_fraction = ((ratio - 1.0).abs() > self.clip_range).float().mean()
                
                total_policy_loss += policy_loss.item()
                total_value_loss += value_loss.item()
                total_safety_loss += safety_loss.item()
                total_constraint_loss += constraint_loss.item()
                total_entropy_loss += entropy.item()
                total_kl_div += kl_div.item()
                total_clip_fraction += clip_fraction.item()
        
        # Update metrics
        n_updates = self.n_epochs * (len(observations) // self.batch_size)
        self.training_metrics.update({
            "policy_loss": total_policy_loss / n_updates,
            "value_loss": total_value_loss / n_updates,
            "safety_loss": total_safety_loss / n_updates,
            "constraint_loss": total_constraint_loss / n_updates,
            "entropy_loss": total_entropy_loss / n_updates,
            "total_loss": (total_policy_loss + total_value_loss + total_safety_loss) / n_updates,
            "kl_divergence": total_kl_div / n_updates,
            "clip_fraction": total_clip_fraction / n_updates,
            "cost_violation": costs.mean().item(),
        })
        
        # Update safety metrics
        self.safety_metrics.update({
            "violation_rate": (costs > 0).float().mean().item(),
            "constraint_value": costs.mean().item(),
            "lagrange_multiplier": self.lagrange_multiplier.item(),
        })
        
        # Clear buffer
        self.buffer = {key: [] for key in self.buffer.keys()}
        self.training_step += 1
        
        return self.training_metrics
    
    def save(self, path: str):
        """Save agent state"""
        torch.save({
            "actor_critic_state_dict": self.actor_critic.state_dict(),
            "optimizer_state_dict": self.optimizer.state_dict(),
            "lagrange_optimizer_state_dict": self.lagrange_optimizer.state_dict(),
            "lagrange_multiplier": self.lagrange_multiplier,
            "training_step": self.training_step,
            "config": self.config,
        }, path)
        logger.info(f"Saved Safe PPO agent to {path}")
    
    def load(self, path: str):
        """Load agent state"""
        checkpoint = torch.load(path, map_location=self.device)
        self.actor_critic.load_state_dict(checkpoint["actor_critic_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.lagrange_optimizer.load_state_dict(checkpoint["lagrange_optimizer_state_dict"])
        self.lagrange_multiplier = checkpoint["lagrange_multiplier"]
        self.training_step = checkpoint["training_step"]
        logger.info(f"Loaded Safe PPO agent from {path}")
