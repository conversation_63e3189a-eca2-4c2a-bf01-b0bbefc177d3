#!/usr/bin/env python3
"""
Quick Start Script for Safe RL Framework
"""

import sys
import argparse
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

console = Console()

def quick_demo():
    """Run a quick demonstration"""
    console.print(Panel.fit("🚀 Safe RL Framework Quick Demo", style="bold blue"))
    
    # Import after adding to path
    from utils.device_manager import DeviceManager
    from environments.cartpole_safe import SafeCartPoleEnv
    from agents.ppo_agent import PPOAgent
    import torch
    import numpy as np
    
    # Setup device
    device_manager = DeviceManager()
    device = device_manager.get_best_device(prefer_gpu=True)
    
    console.print(f"[green]Using device: {device}[/green]")
    
    # Create environment
    console.print("[yellow]Creating Safe CartPole environment...[/yellow]")
    env = SafeCartPoleEnv(max_episode_steps=200)
    
    # Create agent
    console.print("[yellow]Creating PPO agent...[/yellow]")
    config = {
        "learning_rate": 3e-4,
        "batch_size": 64,
        "device": device,
    }
    agent = PPOAgent(env.observation_space, env.action_space, config, device)
    
    # Run a few episodes
    console.print("[yellow]Running demonstration episodes...[/yellow]")
    
    for episode in range(3):
        obs, info = env.reset()
        total_reward = 0
        safety_violations = 0
        steps = 0
        
        done = False
        while not done and steps < 200:
            action = agent.select_action(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            total_reward += reward
            if info.get("safety_violation", False):
                safety_violations += 1
            steps += 1
        
        console.print(f"Episode {episode + 1}: Reward = {total_reward:.2f}, "
                     f"Steps = {steps}, Safety Violations = {safety_violations}")
    
    env.close()
    console.print("[green]✓ Demo completed successfully![/green]")

def interactive_setup():
    """Interactive setup and configuration"""
    console.print(Panel.fit("🔧 Interactive Setup", style="bold green"))
    
    # Check if user wants to run verification
    if Confirm.ask("Run installation verification?", default=True):
        try:
            from scripts.verify_installation import main as verify_main
            verify_main()
        except Exception as e:
            console.print(f"[red]Verification failed: {e}[/red]")
    
    # Ask about experiment preferences
    console.print("\n[bold]Experiment Configuration[/bold]")
    
    # Environment selection
    environments = ["cartpole_safe", "lunar_safe", "healthcare_sim", "safety_gym"]
    env_choice = Prompt.ask(
        "Choose environment", 
        choices=environments, 
        default="cartpole_safe"
    )
    
    # Agent selection
    agents = ["ppo", "safe_ppo", "meta_ppo", "transformer_ppo"]
    agent_choice = Prompt.ask(
        "Choose agent", 
        choices=agents, 
        default="ppo"
    )
    
    # Training duration
    timesteps = Prompt.ask(
        "Training timesteps", 
        default="50000"
    )
    
    # GPU preference
    use_gpu = Confirm.ask("Use GPU if available?", default=True)
    
    # Generate command
    command = f"python main.py --env {env_choice} --agent {agent_choice} --total_timesteps {timesteps}"
    if use_gpu:
        command += " --gpu"
    
    console.print(f"\n[bold blue]Recommended command:[/bold blue]")
    console.print(f"[cyan]{command}[/cyan]")
    
    # Ask if user wants to run it
    if Confirm.ask("Run this experiment now?", default=False):
        import subprocess
        try:
            subprocess.run(command.split(), check=True)
        except subprocess.CalledProcessError as e:
            console.print(f"[red]Experiment failed: {e}[/red]")
        except KeyboardInterrupt:
            console.print("[yellow]Experiment interrupted by user[/yellow]")

def show_examples():
    """Show usage examples"""
    console.print(Panel.fit("📚 Usage Examples", style="bold magenta"))
    
    examples = [
        {
            "title": "Basic Training",
            "command": "python main.py --env cartpole_safe --agent ppo --gpu",
            "description": "Train PPO agent on Safe CartPole with GPU"
        },
        {
            "title": "Safe Agent Training", 
            "command": "python main.py --env cartpole_safe --agent safe_ppo --total_timesteps 100000",
            "description": "Train Safe PPO agent for 100k timesteps"
        },
        {
            "title": "Meta-Learning Agent",
            "command": "python main.py --env healthcare_sim --agent meta_ppo --meta_learning",
            "description": "Train Meta-PPO on healthcare simulation"
        },
        {
            "title": "Transformer Agent",
            "command": "python main.py --env lunar_safe --agent transformer_ppo --gpu",
            "description": "Train Transformer PPO on Safe Lunar Lander"
        },
        {
            "title": "Run Experiment Suite",
            "command": "python experiments/main_experiments.py",
            "description": "Run complete experiment suite with multiple agents"
        },
        {
            "title": "Generate Analysis",
            "command": "python analysis/generate_report.py results/experiment_name_timestamp/",
            "description": "Generate analysis report for experiment results"
        },
        {
            "title": "Verify Installation",
            "command": "python scripts/verify_installation.py",
            "description": "Verify framework installation and dependencies"
        },
    ]
    
    for i, example in enumerate(examples, 1):
        console.print(f"\n[bold cyan]{i}. {example['title']}[/bold cyan]")
        console.print(f"   [green]{example['command']}[/green]")
        console.print(f"   {example['description']}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Safe RL Framework Quick Start")
    parser.add_argument("--demo", action="store_true", help="Run quick demo")
    parser.add_argument("--setup", action="store_true", help="Interactive setup")
    parser.add_argument("--examples", action="store_true", help="Show usage examples")
    parser.add_argument("--all", action="store_true", help="Run all options")
    
    args = parser.parse_args()
    
    if not any([args.demo, args.setup, args.examples, args.all]):
        # Default: show welcome and examples
        console.print(Panel.fit("🎯 Welcome to Safe RL Meta-Learning Framework", style="bold blue"))
        console.print("\n[bold]This framework provides:[/bold]")
        console.print("• Multiple safe RL agents (PPO, Safe PPO, Meta-PPO, Transformer PPO)")
        console.print("• Safe environments (CartPole, LunarLander, Healthcare, Safety Gym)")
        console.print("• GPU acceleration with CPU fallback")
        console.print("• Comprehensive analysis and visualization tools")
        console.print("• Research-ready experiment infrastructure")
        
        console.print(f"\n[bold]Quick start options:[/bold]")
        console.print(f"  [cyan]python {__file__} --demo[/cyan]     - Run quick demo")
        console.print(f"  [cyan]python {__file__} --setup[/cyan]    - Interactive setup")
        console.print(f"  [cyan]python {__file__} --examples[/cyan] - Show usage examples")
        console.print(f"  [cyan]python {__file__} --all[/cyan]      - Run all options")
        
        return
    
    if args.demo or args.all:
        try:
            quick_demo()
        except Exception as e:
            console.print(f"[red]Demo failed: {e}[/red]")
            console.print("Try running: python scripts/verify_installation.py")
    
    if args.setup or args.all:
        interactive_setup()
    
    if args.examples or args.all:
        show_examples()
    
    console.print("\n[bold green]Happy researching! 🚀[/bold green]")

if __name__ == "__main__":
    main()
