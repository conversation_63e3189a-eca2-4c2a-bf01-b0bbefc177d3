#!/usr/bin/env python3
"""
Generate comprehensive analysis report for Safe RL experiments
"""

import argparse
import pickle
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
from datetime import datetime

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class SafeRLAnalyzer:
    """Analyzer for Safe RL experiment results"""
    
    def __init__(self, results_dir: str):
        self.results_dir = Path(results_dir)
        self.figures_dir = self.results_dir / "figures"
        self.figures_dir.mkdir(exist_ok=True)
        
        # Load results
        self.training_results = self._load_training_results()
        self.config = self._load_config()
        
        # Setup plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def _load_training_results(self) -> Dict[str, Any]:
        """Load training results from pickle file"""
        results_file = self.results_dir / "training_results.pkl"
        if results_file.exists():
            with open(results_file, 'rb') as f:
                return pickle.load(f)
        else:
            console.print(f"[red]Training results not found: {results_file}[/red]")
            return {}
    
    def _load_config(self) -> Dict[str, Any]:
        """Load experiment configuration"""
        config_file = self.results_dir / "config.yaml"
        if config_file.exists():
            try:
                import yaml
                with open(config_file, 'r') as f:
                    return yaml.safe_load(f)
            except yaml.constructor.ConstructorError:
                # Fallback to JSON if YAML has serialization issues
                try:
                    import json
                    with open(config_file, 'r') as f:
                        content = f.read()
                        # Try to extract basic info manually
                        return {"experiment_name": "unknown", "agent": "unknown"}
                except:
                    return {"experiment_name": "unknown", "agent": "unknown"}
        else:
            return {}
    
    def generate_training_curves(self):
        """Generate training curve plots"""
        if not self.training_results:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Progress', fontsize=16, fontweight='bold')
        
        # Reward curve
        if self.training_results.get("rewards"):
            axes[0, 0].plot(self.training_results["episodes"], self.training_results["rewards"])
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Episode length
        if self.training_results.get("episode_lengths"):
            axes[0, 1].plot(self.training_results["episodes"], self.training_results["episode_lengths"])
            axes[0, 1].set_title('Episode Lengths')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Steps')
            axes[0, 1].grid(True, alpha=0.3)
        
        # Safety violations
        if self.training_results.get("safety_violations"):
            axes[1, 0].plot(self.training_results["episodes"], 
                           np.cumsum(self.training_results["safety_violations"]))
            axes[1, 0].set_title('Cumulative Safety Violations')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Total Violations')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Constraint violations
        if self.training_results.get("constraint_violations"):
            axes[1, 1].plot(self.training_results["episodes"], 
                           np.cumsum(self.training_results["constraint_violations"]))
            axes[1, 1].set_title('Cumulative Constraint Violations')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Total Violations')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.figures_dir / "training_curves.png", dpi=300, bbox_inches='tight')
        plt.savefig(self.figures_dir / "training_curves.pdf", bbox_inches='tight')
        plt.close()
        
        console.print("✓ Generated training curves")
    
    def generate_safety_analysis(self):
        """Generate safety-specific analysis plots"""
        if not self.training_results:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Safety Analysis', fontsize=16, fontweight='bold')
        
        # Safety violation rate over time
        if self.training_results.get("safety_violations"):
            window_size = max(10, len(self.training_results["safety_violations"]) // 20)
            violation_rate = pd.Series(self.training_results["safety_violations"]).rolling(window_size).mean()
            axes[0, 0].plot(self.training_results["episodes"], violation_rate)
            axes[0, 0].set_title(f'Safety Violation Rate (Rolling Average, window={window_size})')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Violations per Episode')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Reward vs Safety trade-off
        if (self.training_results.get("rewards") and 
            self.training_results.get("safety_violations")):
            
            # Compute rolling averages
            window_size = max(10, len(self.training_results["rewards"]) // 20)
            avg_rewards = pd.Series(self.training_results["rewards"]).rolling(window_size).mean()
            avg_violations = pd.Series(self.training_results["safety_violations"]).rolling(window_size).mean()
            
            scatter = axes[0, 1].scatter(avg_violations, avg_rewards, 
                                       c=self.training_results["episodes"], 
                                       cmap='viridis', alpha=0.6)
            axes[0, 1].set_title('Reward vs Safety Trade-off')
            axes[0, 1].set_xlabel('Average Safety Violations')
            axes[0, 1].set_ylabel('Average Reward')
            axes[0, 1].grid(True, alpha=0.3)
            plt.colorbar(scatter, ax=axes[0, 1], label='Episode')
        
        # Safety budget usage
        if self.training_results.get("evaluation_results"):
            eval_episodes = [r["episode"] for r in self.training_results["evaluation_results"]]
            safety_budget_used = [r.get("safety_budget_used", 0) for r in self.training_results["evaluation_results"]]
            
            axes[1, 0].plot(eval_episodes, safety_budget_used)
            axes[1, 0].axhline(y=1.0, color='r', linestyle='--', label='Budget Limit')
            axes[1, 0].set_title('Safety Budget Usage')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Budget Used (fraction)')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # Violation distribution
        if self.training_results.get("safety_violations"):
            violations = self.training_results["safety_violations"]
            axes[1, 1].hist(violations, bins=max(5, int(np.sqrt(len(violations)))), 
                           alpha=0.7, edgecolor='black')
            axes[1, 1].set_title('Safety Violation Distribution')
            axes[1, 1].set_xlabel('Violations per Episode')
            axes[1, 1].set_ylabel('Frequency')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.figures_dir / "safety_analysis.png", dpi=300, bbox_inches='tight')
        plt.savefig(self.figures_dir / "safety_analysis.pdf", bbox_inches='tight')
        plt.close()
        
        console.print("✓ Generated safety analysis")
    
    def generate_training_metrics(self):
        """Generate training metrics plots"""
        if not self.training_results.get("training_metrics"):
            return
        
        metrics_data = self.training_results["training_metrics"]
        if not metrics_data:
            return
        
        # Extract metrics
        timesteps = [m["timestep"] for m in metrics_data]
        policy_loss = [m.get("policy_loss", 0) for m in metrics_data]
        value_loss = [m.get("value_loss", 0) for m in metrics_data]
        entropy_loss = [m.get("entropy_loss", 0) for m in metrics_data]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Metrics', fontsize=16, fontweight='bold')
        
        # Policy loss
        axes[0, 0].plot(timesteps, policy_loss)
        axes[0, 0].set_title('Policy Loss')
        axes[0, 0].set_xlabel('Timestep')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Value loss
        axes[0, 1].plot(timesteps, value_loss)
        axes[0, 1].set_title('Value Loss')
        axes[0, 1].set_xlabel('Timestep')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Entropy loss
        axes[1, 0].plot(timesteps, entropy_loss)
        axes[1, 0].set_title('Entropy Loss')
        axes[1, 0].set_xlabel('Timestep')
        axes[1, 0].set_ylabel('Loss')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Combined losses
        axes[1, 1].plot(timesteps, policy_loss, label='Policy Loss')
        axes[1, 1].plot(timesteps, value_loss, label='Value Loss')
        axes[1, 1].plot(timesteps, entropy_loss, label='Entropy Loss')
        axes[1, 1].set_title('All Losses')
        axes[1, 1].set_xlabel('Timestep')
        axes[1, 1].set_ylabel('Loss')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.figures_dir / "training_metrics.png", dpi=300, bbox_inches='tight')
        plt.savefig(self.figures_dir / "training_metrics.pdf", bbox_inches='tight')
        plt.close()
        
        console.print("✓ Generated training metrics")
    
    def generate_evaluation_analysis(self):
        """Generate evaluation results analysis"""
        if not self.training_results.get("evaluation_results"):
            return
        
        eval_data = self.training_results["evaluation_results"]
        if not eval_data:
            return
        
        # Extract evaluation metrics
        timesteps = [e["timestep"] for e in eval_data]
        mean_rewards = [e.get("mean_reward", 0) for e in eval_data]
        std_rewards = [e.get("std_reward", 0) for e in eval_data]
        safety_scores = [e.get("safety_score", 0) for e in eval_data]
        violation_rates = [e.get("violation_rate", 0) for e in eval_data]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Evaluation Results', fontsize=16, fontweight='bold')
        
        # Mean reward with error bars
        axes[0, 0].errorbar(timesteps, mean_rewards, yerr=std_rewards, 
                           capsize=3, capthick=1, alpha=0.8)
        axes[0, 0].set_title('Evaluation Rewards')
        axes[0, 0].set_xlabel('Timestep')
        axes[0, 0].set_ylabel('Mean Reward ± Std')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Safety score
        axes[0, 1].plot(timesteps, safety_scores, marker='o')
        axes[0, 1].set_title('Safety Score (Reward - Safety Penalty)')
        axes[0, 1].set_xlabel('Timestep')
        axes[0, 1].set_ylabel('Safety Score')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Violation rate
        axes[1, 0].plot(timesteps, violation_rates, marker='s', color='red')
        axes[1, 0].set_title('Safety Violation Rate')
        axes[1, 0].set_xlabel('Timestep')
        axes[1, 0].set_ylabel('Violation Rate')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Reward vs Safety Score
        axes[1, 1].scatter(mean_rewards, safety_scores, c=timesteps, cmap='viridis')
        axes[1, 1].set_title('Reward vs Safety Score')
        axes[1, 1].set_xlabel('Mean Reward')
        axes[1, 1].set_ylabel('Safety Score')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.figures_dir / "evaluation_analysis.png", dpi=300, bbox_inches='tight')
        plt.savefig(self.figures_dir / "evaluation_analysis.pdf", bbox_inches='tight')
        plt.close()
        
        console.print("✓ Generated evaluation analysis")
    
    def generate_summary_statistics(self) -> Dict[str, Any]:
        """Generate summary statistics"""
        stats = {}
        
        if self.training_results.get("rewards"):
            rewards = self.training_results["rewards"]
            stats["reward_stats"] = {
                "mean": np.mean(rewards),
                "std": np.std(rewards),
                "min": np.min(rewards),
                "max": np.max(rewards),
                "final": rewards[-1] if rewards else 0,
            }
        
        if self.training_results.get("safety_violations"):
            violations = self.training_results["safety_violations"]
            stats["safety_stats"] = {
                "total_violations": np.sum(violations),
                "violation_rate": np.mean(violations),
                "episodes_with_violations": np.sum([v > 0 for v in violations]),
                "max_violations_per_episode": np.max(violations),
            }
        
        if self.training_results.get("evaluation_results"):
            eval_data = self.training_results["evaluation_results"]
            final_eval = eval_data[-1] if eval_data else {}
            stats["final_evaluation"] = final_eval
        
        # Training efficiency
        if self.training_results.get("timesteps"):
            total_timesteps = self.training_results["timesteps"][-1] if self.training_results["timesteps"] else 0
            total_episodes = len(self.training_results.get("episodes", []))
            stats["training_efficiency"] = {
                "total_timesteps": total_timesteps,
                "total_episodes": total_episodes,
                "avg_episode_length": total_timesteps / total_episodes if total_episodes > 0 else 0,
            }
        
        return stats
    
    def generate_report(self):
        """Generate complete analysis report"""
        console.print(f"[bold blue]Generating analysis report for: {self.results_dir}[/bold blue]")
        
        with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}")) as progress:
            task = progress.add_task("Generating plots...", total=5)
            
            # Generate all plots
            self.generate_training_curves()
            progress.advance(task)
            
            self.generate_safety_analysis()
            progress.advance(task)
            
            self.generate_training_metrics()
            progress.advance(task)
            
            self.generate_evaluation_analysis()
            progress.advance(task)
            
            # Generate summary statistics
            stats = self.generate_summary_statistics()
            
            # Save statistics
            with open(self.results_dir / "analysis_summary.json", 'w') as f:
                json.dump(stats, f, indent=2, default=str)
            
            progress.advance(task)
        
        console.print(f"[green]✓ Analysis complete! Figures saved to: {self.figures_dir}[/green]")
        
        # Print summary
        if stats:
            console.print("\n[bold]Summary Statistics:[/bold]")
            if "reward_stats" in stats:
                r_stats = stats["reward_stats"]
                console.print(f"  Final Reward: {r_stats['final']:.2f}")
                console.print(f"  Mean Reward: {r_stats['mean']:.2f} ± {r_stats['std']:.2f}")
            
            if "safety_stats" in stats:
                s_stats = stats["safety_stats"]
                console.print(f"  Total Safety Violations: {s_stats['total_violations']}")
                console.print(f"  Violation Rate: {s_stats['violation_rate']:.4f}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Generate Safe RL analysis report")
    parser.add_argument("results_dir", type=str, help="Path to results directory")
    
    args = parser.parse_args()
    
    if not Path(args.results_dir).exists():
        console.print(f"[red]Results directory not found: {args.results_dir}[/red]")
        return
    
    analyzer = SafeRLAnalyzer(args.results_dir)
    analyzer.generate_report()

if __name__ == "__main__":
    main()
