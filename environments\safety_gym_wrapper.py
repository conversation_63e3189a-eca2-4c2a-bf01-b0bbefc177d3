"""
Safety Gym Wrapper for Safe RL
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class SafetyGymWrapper(gym.Env):
    """
    Wrapper for Safety Gym environments with enhanced safety tracking
    
    Falls back to a simple safe navigation environment if Safety Gym is not available
    """
    
    metadata = {"render_modes": ["human", "rgb_array"], "render_fps": 30}
    
    def __init__(self, 
                 config: Dict[str, Any] = None,
                 max_episode_steps: int = 1000,
                 safety_budget: float = 0.1,
                 **kwargs):
        
        super().__init__()
        
        self.config = config or {}
        self.max_episode_steps = max_episode_steps
        self.safety_budget = safety_budget
        
        # Try to create Safety Gym environment
        self.safety_gym_available = False
        self.base_env = None
        
        try:
            import safety_gym
            
            # Get environment parameters from config
            task = self.config.get("task", "goal")
            robot = self.config.get("robot", "point")
            env_name = f"Safexp-{robot.capitalize()}{task.capitalize()}1-v0"
            
            self.base_env = gym.make(env_name)
            self.safety_gym_available = True
            
            # Use Safety Gym spaces
            self.observation_space = self.base_env.observation_space
            self.action_space = self.base_env.action_space
            
            logger.info(f"Created Safety Gym environment: {env_name}")
            
        except ImportError:
            logger.warning("Safety Gym not available, using fallback safe navigation environment")
            self._create_fallback_env()
        except Exception as e:
            logger.warning(f"Failed to create Safety Gym environment: {e}, using fallback")
            self._create_fallback_env()
        
        # Safety tracking
        self.current_step = 0
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        
    def _create_fallback_env(self):
        """Create fallback safe navigation environment"""
        self.safety_gym_available = False
        
        # Simple 2D navigation with obstacles
        # Observation: [x, y, vx, vy, goal_x, goal_y, obstacle_distances...]
        self.observation_space = spaces.Box(
            low=-10.0, high=10.0, shape=(10,), dtype=np.float32
        )
        
        # Action: [forward/backward, left/right]
        self.action_space = spaces.Box(
            low=-1.0, high=1.0, shape=(2,), dtype=np.float32
        )
        
        # Environment state
        self.agent_pos = np.array([0.0, 0.0])
        self.agent_vel = np.array([0.0, 0.0])
        self.goal_pos = np.array([5.0, 5.0])
        
        # Obstacles (hazards)
        self.obstacles = [
            {"pos": np.array([2.0, 2.0]), "radius": 0.5},
            {"pos": np.array([3.0, 4.0]), "radius": 0.7},
            {"pos": np.array([1.0, 4.5]), "radius": 0.4},
            {"pos": np.array([4.5, 1.5]), "radius": 0.6},
        ]
        
        # Safe zones
        self.safe_zones = [
            {"pos": np.array([1.0, 1.0]), "radius": 0.8},
            {"pos": np.array([4.0, 3.0]), "radius": 0.6},
        ]
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset environment to initial state"""
        super().reset(seed=seed)
        
        if self.safety_gym_available and self.base_env:
            obs, info = self.base_env.reset(seed=seed, options=options)
        else:
            # Reset fallback environment
            self.agent_pos = self.np_random.uniform(-1.0, 1.0, size=2)
            self.agent_vel = np.array([0.0, 0.0])
            self.goal_pos = self.np_random.uniform(4.0, 6.0, size=2)
            obs = self._get_fallback_observation()
            info = {}
        
        # Reset safety tracking
        self.current_step = 0
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        
        enhanced_info = self._get_info()
        enhanced_info.update(info)
        
        return obs, enhanced_info
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Execute one step in the environment"""
        
        if self.safety_gym_available and self.base_env:
            obs, reward, terminated, truncated, info = self.base_env.step(action)
            
            # Extract safety information from Safety Gym
            cost = info.get("cost", 0.0)
            safety_violation = cost > 0
            constraint_violation = info.get("constraint_violation", False)
            
        else:
            # Fallback environment step
            obs, reward, terminated, truncated, info, cost, safety_violation, constraint_violation = self._fallback_step(action)
        
        self.current_step += 1
        
        # Update safety tracking
        if safety_violation:
            self.safety_violations += 1
        if constraint_violation:
            self.constraint_violations += 1
        self.cumulative_cost += cost
        
        # Check truncation
        truncated = truncated or self.current_step >= self.max_episode_steps
        
        # Enhanced info
        enhanced_info = self._get_info()
        enhanced_info.update(info)
        enhanced_info.update({
            "safety_violation": safety_violation,
            "constraint_violation": constraint_violation,
            "cost": cost,
            "cumulative_cost": self.cumulative_cost,
        })
        
        return obs, reward, terminated, truncated, enhanced_info
    
    def _fallback_step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any], float, bool, bool]:
        """Execute step in fallback environment"""
        # Apply action (simple physics)
        action = np.clip(action, -1.0, 1.0)
        
        # Update velocity
        max_vel = 0.5
        self.agent_vel += action * 0.1
        self.agent_vel = np.clip(self.agent_vel, -max_vel, max_vel)
        
        # Update position
        self.agent_pos += self.agent_vel * 0.1
        
        # Apply friction
        self.agent_vel *= 0.9
        
        # Check collisions with obstacles (safety violations)
        safety_violation = False
        cost = 0.0
        
        for obstacle in self.obstacles:
            dist = np.linalg.norm(self.agent_pos - obstacle["pos"])
            if dist < obstacle["radius"]:
                safety_violation = True
                cost += (obstacle["radius"] - dist) * 10.0
                # Push agent away from obstacle
                direction = (self.agent_pos - obstacle["pos"]) / (dist + 1e-8)
                self.agent_pos = obstacle["pos"] + direction * obstacle["radius"]
        
        # Check if agent is in safe zone (bonus)
        in_safe_zone = False
        for safe_zone in self.safe_zones:
            dist = np.linalg.norm(self.agent_pos - safe_zone["pos"])
            if dist < safe_zone["radius"]:
                in_safe_zone = True
                break
        
        # Constraint violations (going out of bounds)
        constraint_violation = False
        if np.any(np.abs(self.agent_pos) > 8.0):
            constraint_violation = True
            cost += 5.0
            # Clamp position
            self.agent_pos = np.clip(self.agent_pos, -8.0, 8.0)
        
        # Compute reward
        goal_dist = np.linalg.norm(self.agent_pos - self.goal_pos)
        reward = -goal_dist * 0.1  # Distance penalty
        
        if goal_dist < 0.5:  # Reached goal
            reward += 100.0
            terminated = True
        else:
            terminated = False
        
        if in_safe_zone:
            reward += 1.0  # Safe zone bonus
        
        if safety_violation:
            reward -= 20.0  # Safety violation penalty
        
        if constraint_violation:
            reward -= 10.0  # Constraint violation penalty
        
        truncated = False
        info = {
            "goal_distance": goal_dist,
            "in_safe_zone": in_safe_zone,
        }
        
        obs = self._get_fallback_observation()
        
        return obs, reward, terminated, truncated, info, cost, safety_violation, constraint_violation
    
    def _get_fallback_observation(self) -> np.ndarray:
        """Get observation for fallback environment"""
        # Agent position and velocity
        obs = [self.agent_pos[0], self.agent_pos[1], self.agent_vel[0], self.agent_vel[1]]
        
        # Goal position
        obs.extend([self.goal_pos[0], self.goal_pos[1]])
        
        # Distances to obstacles (closest 4)
        obstacle_dists = []
        for obstacle in self.obstacles:
            dist = np.linalg.norm(self.agent_pos - obstacle["pos"])
            obstacle_dists.append(dist)
        
        obs.extend(obstacle_dists)
        
        return np.array(obs, dtype=np.float32)
    
    def _get_info(self) -> Dict[str, Any]:
        """Get enhanced environment info"""
        return {
            "step": self.current_step,
            "safety_violations": self.safety_violations,
            "constraint_violations": self.constraint_violations,
            "cumulative_cost": self.cumulative_cost,
            "safety_budget_used": self.cumulative_cost / (self.safety_budget * self.max_episode_steps),
            "safety_gym_available": self.safety_gym_available,
        }
    
    def render(self):
        """Render the environment"""
        if self.safety_gym_available and self.base_env:
            return self.base_env.render()
        else:
            # Simple text rendering for fallback
            print(f"Agent: ({self.agent_pos[0]:.2f}, {self.agent_pos[1]:.2f}), "
                  f"Goal: ({self.goal_pos[0]:.2f}, {self.goal_pos[1]:.2f}), "
                  f"Violations: {self.safety_violations}")
    
    def close(self):
        """Close the environment"""
        if self.safety_gym_available and self.base_env:
            self.base_env.close()
