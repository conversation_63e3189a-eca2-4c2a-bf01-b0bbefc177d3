"""
Safe CartPole Environment with Safety Constraints
"""

import numpy as np
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, Any, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class SafeCartPoleEnv(gym.Env):
    """
    Safe CartPole environment with angle and position constraints
    
    Safety constraints:
    - Pole angle must stay within safe limits
    - Cart position must stay within safe zone
    - Velocity constraints to prevent dangerous movements
    """
    
    metadata = {"render_modes": ["human", "rgb_array"], "render_fps": 50}
    
    def __init__(self, 
                 max_episode_steps: int = 500,
                 safety_budget: float = 0.1,
                 pole_angle_limit: float = 0.2,  # radians
                 safe_position_limit: float = 1.5,  # meters
                 safe_velocity_limit: float = 2.0,  # m/s
                 constraint_type: str = "hard",
                 render_mode: Optional[str] = None,
                 **kwargs):
        
        super().__init__()
        
        # Environment parameters
        self.max_episode_steps = max_episode_steps
        self.safety_budget = safety_budget
        self.pole_angle_limit = pole_angle_limit
        self.safe_position_limit = safe_position_limit
        self.safe_velocity_limit = safe_velocity_limit
        self.constraint_type = constraint_type
        self.render_mode = render_mode
        
        # CartPole physics parameters
        self.gravity = 9.8
        self.masscart = 1.0
        self.masspole = 0.1
        self.total_mass = self.masspole + self.masscart
        self.length = 0.5  # actually half the pole's length
        self.polemass_length = self.masspole * self.length
        self.force_mag = 10.0
        self.tau = 0.02  # seconds between state updates
        
        # Thresholds for episode termination
        self.theta_threshold_radians = 12 * 2 * np.pi / 360  # 12 degrees
        self.x_threshold = 2.4
        
        # Action and observation spaces
        self.action_space = spaces.Discrete(2)
        
        # Observations: [x, x_dot, theta, theta_dot, safety_indicator]
        high = np.array([
            self.x_threshold * 2,
            np.finfo(np.float32).max,
            self.theta_threshold_radians * 2,
            np.finfo(np.float32).max,
            1.0,  # safety indicator
        ], dtype=np.float32)
        
        self.observation_space = spaces.Box(-high, high, dtype=np.float32)
        
        # State variables
        self.state = None
        self.steps_beyond_done = None
        self.current_step = 0
        
        # Safety tracking
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        
        # Rendering
        self.screen_width = 600
        self.screen_height = 400
        self.screen = None
        self.clock = None
        self.isopen = True
    
    def reset(self, seed: Optional[int] = None, options: Optional[Dict] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """Reset environment to initial state"""
        super().reset(seed=seed)
        
        # Reset state
        self.state = self.np_random.uniform(low=-0.05, high=0.05, size=(4,))
        self.steps_beyond_done = None
        self.current_step = 0
        
        # Reset safety tracking
        self.safety_violations = 0
        self.constraint_violations = 0
        self.cumulative_cost = 0.0
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """Execute one step in the environment"""
        assert self.action_space.contains(action), f"Invalid action {action}"
        
        x, x_dot, theta, theta_dot = self.state
        force = self.force_mag if action == 1 else -self.force_mag
        
        # Physics simulation
        costheta = np.cos(theta)
        sintheta = np.sin(theta)
        
        temp = (force + self.polemass_length * theta_dot**2 * sintheta) / self.total_mass
        thetaacc = (self.gravity * sintheta - costheta * temp) / (
            self.length * (4.0 / 3.0 - self.masspole * costheta**2 / self.total_mass)
        )
        xacc = temp - self.polemass_length * thetaacc * costheta / self.total_mass
        
        # Update state
        x = x + self.tau * x_dot
        x_dot = x_dot + self.tau * xacc
        theta = theta + self.tau * theta_dot
        theta_dot = theta_dot + self.tau * thetaacc
        
        self.state = np.array([x, x_dot, theta, theta_dot], dtype=np.float32)
        self.current_step += 1
        
        # Check safety constraints
        safety_violation, constraint_violation, cost = self._check_safety_constraints()
        
        # Check termination conditions
        terminated = bool(
            x < -self.x_threshold
            or x > self.x_threshold
            or theta < -self.theta_threshold_radians
            or theta > self.theta_threshold_radians
        )
        
        # Check truncation (max steps)
        truncated = self.current_step >= self.max_episode_steps
        
        # Compute reward
        reward = self._compute_reward(safety_violation, constraint_violation)
        
        # Update safety tracking
        if safety_violation:
            self.safety_violations += 1
        if constraint_violation:
            self.constraint_violations += 1
        self.cumulative_cost += cost
        
        # Get observation and info
        observation = self._get_observation()
        info = self._get_info()
        info.update({
            "safety_violation": safety_violation,
            "constraint_violation": constraint_violation,
            "cost": cost,
            "cumulative_cost": self.cumulative_cost,
        })
        
        return observation, reward, terminated, truncated, info
    
    def _check_safety_constraints(self) -> Tuple[bool, bool, float]:
        """Check safety constraints and return violations and cost"""
        x, x_dot, theta, theta_dot = self.state
        
        safety_violation = False
        constraint_violation = False
        cost = 0.0
        
        # Pole angle constraint
        if abs(theta) > self.pole_angle_limit:
            safety_violation = True
            cost += abs(theta) - self.pole_angle_limit
        
        # Position constraint
        if abs(x) > self.safe_position_limit:
            constraint_violation = True
            cost += abs(x) - self.safe_position_limit
        
        # Velocity constraint
        if abs(x_dot) > self.safe_velocity_limit:
            constraint_violation = True
            cost += (abs(x_dot) - self.safe_velocity_limit) * 0.1
        
        # Angular velocity constraint
        if abs(theta_dot) > 3.0:  # rad/s
            safety_violation = True
            cost += (abs(theta_dot) - 3.0) * 0.1
        
        return safety_violation, constraint_violation, cost
    
    def _compute_reward(self, safety_violation: bool, constraint_violation: bool) -> float:
        """Compute reward with safety penalties"""
        # Base reward for staying upright
        reward = 1.0
        
        # Safety penalties
        if safety_violation:
            reward -= 10.0  # Large penalty for safety violations
        
        if constraint_violation:
            reward -= 2.0   # Moderate penalty for constraint violations
        
        # Bonus for staying in safe zone
        x, x_dot, theta, theta_dot = self.state
        if abs(theta) < self.pole_angle_limit * 0.5 and abs(x) < self.safe_position_limit * 0.5:
            reward += 0.5  # Bonus for being in very safe zone
        
        return reward
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation with safety indicator"""
        x, x_dot, theta, theta_dot = self.state
        
        # Safety indicator (0 = safe, 1 = unsafe)
        safety_indicator = 0.0
        if (abs(theta) > self.pole_angle_limit * 0.8 or 
            abs(x) > self.safe_position_limit * 0.8):
            safety_indicator = 1.0
        
        observation = np.array([x, x_dot, theta, theta_dot, safety_indicator], dtype=np.float32)
        return observation
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info"""
        x, x_dot, theta, theta_dot = self.state
        
        return {
            "step": self.current_step,
            "safety_violations": self.safety_violations,
            "constraint_violations": self.constraint_violations,
            "cumulative_cost": self.cumulative_cost,
            "pole_angle": theta,
            "cart_position": x,
            "cart_velocity": x_dot,
            "pole_velocity": theta_dot,
            "safety_budget_used": self.cumulative_cost / (self.safety_budget * self.max_episode_steps),
        }
    
    def render(self):
        """Render the environment"""
        if self.render_mode is None:
            return
        
        try:
            import pygame
        except ImportError:
            logger.warning("pygame not available for rendering")
            return
        
        if self.screen is None:
            pygame.init()
            if self.render_mode == "human":
                pygame.display.init()
                self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
            else:  # rgb_array
                self.screen = pygame.Surface((self.screen_width, self.screen_height))
        
        if self.clock is None:
            self.clock = pygame.time.Clock()
        
        # Colors
        WHITE = (255, 255, 255)
        BLACK = (0, 0, 0)
        RED = (255, 0, 0)
        GREEN = (0, 255, 0)
        BLUE = (0, 0, 255)
        
        self.screen.fill(WHITE)
        
        # Get state
        x, x_dot, theta, theta_dot = self.state
        
        # Cart and pole positions
        cart_x = int(x * 100 + self.screen_width // 2)
        cart_y = self.screen_height // 2
        
        pole_length = 100
        pole_x = int(cart_x + pole_length * np.sin(theta))
        pole_y = int(cart_y - pole_length * np.cos(theta))
        
        # Draw safety zones
        safe_zone_width = int(self.safe_position_limit * 100)
        pygame.draw.rect(self.screen, (200, 255, 200), 
                        (self.screen_width // 2 - safe_zone_width, cart_y - 10,
                         2 * safe_zone_width, 20))
        
        # Draw cart
        cart_color = RED if abs(x) > self.safe_position_limit else GREEN
        pygame.draw.rect(self.screen, cart_color, (cart_x - 15, cart_y - 10, 30, 20))
        
        # Draw pole
        pole_color = RED if abs(theta) > self.pole_angle_limit else BLUE
        pygame.draw.line(self.screen, pole_color, (cart_x, cart_y), (pole_x, pole_y), 5)
        
        # Draw safety indicators
        font = pygame.font.Font(None, 36)
        safety_text = f"Violations: {self.safety_violations}"
        text_surface = font.render(safety_text, True, BLACK)
        self.screen.blit(text_surface, (10, 10))
        
        constraint_text = f"Constraints: {self.constraint_violations}"
        text_surface = font.render(constraint_text, True, BLACK)
        self.screen.blit(text_surface, (10, 50))
        
        if self.render_mode == "human":
            pygame.display.flip()
            self.clock.tick(self.metadata["render_fps"])
        else:  # rgb_array
            return np.transpose(
                np.array(pygame.surfarray.pixels3d(self.screen)), axes=(1, 0, 2)
            )
    
    def close(self):
        """Close the environment"""
        if self.screen is not None:
            import pygame
            pygame.display.quit()
            pygame.quit()
            self.isopen = False
