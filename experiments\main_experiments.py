#!/usr/bin/env python3
"""
Main Experiments Runner for Safe RL Framework
"""

import argparse
import sys
import time
from pathlib import Path
from typing import Dict, List, Any
import logging
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn

from utils.device_manager import DeviceManager
from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from agents.agent_factory import AgentFactory
from environments.env_factory import EnvironmentFactory
from experiments.training_runner import TrainingRunner

console = Console()

class ExperimentSuite:
    """Manages and runs multiple Safe RL experiments"""
    
    def __init__(self, config_path: str = None):
        self.config_manager = ConfigManager()
        self.device_manager = DeviceManager()
        self.agent_factory = AgentFactory()
        self.env_factory = EnvironmentFactory()
        
        # Load experiment configuration
        if config_path:
            self.experiment_config = self.config_manager.load_config(config_path)
        else:
            self.experiment_config = self._get_default_experiment_config()
        
        # Setup logger
        self.logger = setup_logger("experiment_suite", console=console)
        
        # Results storage
        self.experiment_results = []
    
    def _get_default_experiment_config(self) -> Dict[str, Any]:
        """Get default experiment configuration"""
        return {
            "experiments": [
                {
                    "name": "ppo_cartpole",
                    "agent": "ppo",
                    "environment": "cartpole_safe",
                    "seeds": [42, 123, 456],
                    "total_timesteps": 50000,
                },
                {
                    "name": "safe_ppo_cartpole", 
                    "agent": "safe_ppo",
                    "environment": "cartpole_safe",
                    "seeds": [42, 123, 456],
                    "total_timesteps": 50000,
                },
                {
                    "name": "meta_ppo_cartpole",
                    "agent": "meta_ppo", 
                    "environment": "cartpole_safe",
                    "seeds": [42, 123, 456],
                    "total_timesteps": 50000,
                },
                {
                    "name": "transformer_ppo_cartpole",
                    "agent": "transformer_ppo",
                    "environment": "cartpole_safe", 
                    "seeds": [42, 123, 456],
                    "total_timesteps": 50000,
                },
            ],
            "global_config": {
                "eval_freq": 5000,
                "n_eval_episodes": 10,
                "save_model": True,
                "early_stopping": False,
            }
        }
    
    def run_single_experiment(self, exp_config: Dict[str, Any], seed: int) -> Dict[str, Any]:
        """Run a single experiment with given configuration and seed"""
        
        # Create experiment name with seed
        exp_name = f"{exp_config['name']}_seed_{seed}"
        
        # Setup configuration
        config = self.config_manager.get_default_config(
            exp_config["environment"], 
            exp_config["agent"]
        )
        
        # Override with experiment-specific config
        config.update(self.experiment_config.get("global_config", {}))
        config.update({
            "experiment_name": exp_name,
            "seed": seed,
            "total_timesteps": exp_config.get("total_timesteps", 50000),
            "device": self.device_manager.get_best_device(prefer_gpu=True),
        })
        
        # Override with any experiment-specific parameters
        if "config_overrides" in exp_config:
            config.update(exp_config["config_overrides"])
        
        self.logger.info(f"Starting experiment: {exp_name}")
        
        try:
            # Create environment
            env = self.env_factory.create_environment(exp_config["environment"], config)
            
            # Create agent
            agent = self.agent_factory.create_agent(exp_config["agent"], env, config)
            
            # Create training runner
            runner = TrainingRunner(agent, env, config, self.logger)
            
            # Run training
            start_time = time.time()
            results = runner.run()
            training_time = time.time() - start_time
            
            # Add experiment metadata to results
            results.update({
                "experiment_name": exp_name,
                "agent_type": exp_config["agent"],
                "environment": exp_config["environment"],
                "seed": seed,
                "training_time": training_time,
                "config": config,
            })
            
            self.logger.info(f"Completed experiment: {exp_name}")
            return results
            
        except Exception as e:
            self.logger.error(f"Experiment {exp_name} failed: {str(e)}")
            return {
                "experiment_name": exp_name,
                "agent_type": exp_config["agent"],
                "environment": exp_config["environment"],
                "seed": seed,
                "error": str(e),
                "success": False,
            }
    
    def run_experiment_suite(self) -> List[Dict[str, Any]]:
        """Run complete experiment suite"""
        
        self.logger.info("Starting Safe RL Experiment Suite")
        
        # Calculate total experiments
        total_experiments = sum(
            len(exp.get("seeds", [42])) 
            for exp in self.experiment_config["experiments"]
        )
        
        console.print(f"[bold blue]Running {total_experiments} experiments[/bold blue]")
        
        # Display experiment plan
        self._display_experiment_plan()
        
        # Run experiments
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            console=console
        ) as progress:
            
            task = progress.add_task("Running experiments", total=total_experiments)
            
            for exp_config in self.experiment_config["experiments"]:
                seeds = exp_config.get("seeds", [42])
                
                for seed in seeds:
                    progress.update(task, description=f"Running {exp_config['name']} (seed {seed})")
                    
                    result = self.run_single_experiment(exp_config, seed)
                    self.experiment_results.append(result)
                    
                    progress.advance(task)
        
        # Generate summary
        self._generate_summary()
        
        return self.experiment_results
    
    def _display_experiment_plan(self):
        """Display experiment plan table"""
        table = Table(title="Experiment Plan", show_header=True, header_style="bold magenta")
        table.add_column("Experiment", style="cyan")
        table.add_column("Agent", style="green")
        table.add_column("Environment", style="yellow")
        table.add_column("Seeds", style="blue")
        table.add_column("Timesteps", style="red")
        
        for exp in self.experiment_config["experiments"]:
            seeds_str = ", ".join(map(str, exp.get("seeds", [42])))
            table.add_row(
                exp["name"],
                exp["agent"],
                exp["environment"],
                seeds_str,
                str(exp.get("total_timesteps", 50000))
            )
        
        console.print(table)
    
    def _generate_summary(self):
        """Generate and display experiment summary"""
        console.print("\n[bold blue]Experiment Suite Summary[/bold blue]")
        
        # Success rate
        successful = [r for r in self.experiment_results if r.get("success", True)]
        success_rate = len(successful) / len(self.experiment_results) if self.experiment_results else 0
        
        console.print(f"Success Rate: {success_rate:.1%} ({len(successful)}/{len(self.experiment_results)})")
        
        if not successful:
            console.print("[red]No successful experiments to analyze[/red]")
            return
        
        # Results by agent type
        agent_results = {}
        for result in successful:
            agent_type = result["agent_type"]
            if agent_type not in agent_results:
                agent_results[agent_type] = []
            agent_results[agent_type].append(result)
        
        # Summary table
        summary_table = Table(show_header=True, header_style="bold magenta")
        summary_table.add_column("Agent", style="cyan")
        summary_table.add_column("Runs", style="blue")
        summary_table.add_column("Avg Reward", style="green")
        summary_table.add_column("Avg Violations", style="red")
        summary_table.add_column("Avg Training Time", style="yellow")
        
        for agent_type, results in agent_results.items():
            avg_reward = np.mean([r.get("final_reward", 0) for r in results])
            avg_violations = np.mean([r.get("total_violations", 0) for r in results])
            avg_time = np.mean([r.get("training_time", 0) for r in results])
            
            summary_table.add_row(
                agent_type,
                str(len(results)),
                f"{avg_reward:.2f}",
                f"{avg_violations:.1f}",
                f"{avg_time:.1f}s"
            )
        
        console.print(summary_table)
        
        # Save detailed results
        self._save_results()
    
    def _save_results(self):
        """Save experiment results"""
        import json
        
        # Create results directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("results") / f"experiment_suite_{timestamp}"
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Save detailed results
        with open(results_dir / "experiment_results.json", 'w') as f:
            json.dump(self.experiment_results, f, indent=2, default=str)
        
        # Save configuration
        with open(results_dir / "experiment_config.json", 'w') as f:
            json.dump(self.experiment_config, f, indent=2, default=str)
        
        console.print(f"[green]Results saved to: {results_dir}[/green]")
        
        # Generate analysis for each successful experiment
        for result in self.experiment_results:
            if result.get("success", True) and "experiment_name" in result:
                try:
                    # Try to generate analysis report
                    from analysis.generate_report import SafeRLAnalyzer
                    
                    exp_results_dir = Path("results") / result["experiment_name"]
                    if exp_results_dir.exists():
                        analyzer = SafeRLAnalyzer(str(exp_results_dir))
                        analyzer.generate_report()
                except Exception as e:
                    self.logger.warning(f"Failed to generate analysis for {result['experiment_name']}: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run Safe RL Experiment Suite")
    parser.add_argument("--config", type=str, help="Path to experiment configuration file")
    parser.add_argument("--single", type=str, help="Run single experiment by name")
    parser.add_argument("--list", action="store_true", help="List available experiments")
    parser.add_argument("--gpu", action="store_true", help="Prefer GPU if available")
    
    args = parser.parse_args()
    
    # Create experiment suite
    suite = ExperimentSuite(args.config)
    
    if args.list:
        # List available experiments
        console.print("[bold]Available Experiments:[/bold]")
        for exp in suite.experiment_config["experiments"]:
            console.print(f"  - {exp['name']}: {exp['agent']} on {exp['environment']}")
        return
    
    if args.single:
        # Run single experiment
        exp_config = None
        for exp in suite.experiment_config["experiments"]:
            if exp["name"] == args.single:
                exp_config = exp
                break
        
        if not exp_config:
            console.print(f"[red]Experiment '{args.single}' not found[/red]")
            return
        
        console.print(f"[bold]Running single experiment: {args.single}[/bold]")
        seeds = exp_config.get("seeds", [42])
        
        for seed in seeds:
            result = suite.run_single_experiment(exp_config, seed)
            console.print(f"Result: {result.get('final_reward', 'Failed')}")
    
    else:
        # Run full experiment suite
        suite.run_experiment_suite()

if __name__ == "__main__":
    # Import numpy for summary calculations
    import numpy as np
    main()
